/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/reviews/route";
exports.ids = ["app/api/reviews/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freviews%2Froute&page=%2Fapi%2Freviews%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freviews%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freviews%2Froute&page=%2Fapi%2Freviews%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freviews%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_website_ebammotors_src_app_api_reviews_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/reviews/route.ts */ \"(rsc)/./src/app/api/reviews/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/reviews/route\",\n        pathname: \"/api/reviews\",\n        filename: \"route\",\n        bundlePath: \"app/api/reviews/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\app\\\\api\\\\reviews\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_website_ebammotors_src_app_api_reviews_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freviews%2Froute&page=%2Fapi%2Freviews%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freviews%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/reviews/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/reviews/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_reviewStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/reviewStorage */ \"(rsc)/./src/lib/reviewStorage.ts\");\n/* harmony import */ var _lib_resendService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/resendService */ \"(rsc)/./src/lib/resendService.ts\");\n/* harmony import */ var _lib_adminMiddleware__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/adminMiddleware */ \"(rsc)/./src/lib/adminMiddleware.ts\");\n/* harmony import */ var _lib_security__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/security */ \"(rsc)/./src/lib/security.ts\");\n/* harmony import */ var _lib_monitoring__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/monitoring */ \"(rsc)/./src/lib/monitoring.ts\");\n\n\n\n\n\n\nasync function POST(request) {\n    try {\n        const formData = await request.formData();\n        // Extract and validate form fields\n        const name = formData.get('name');\n        const location = formData.get('location');\n        const email = formData.get('email');\n        const rating = formData.get('rating');\n        const review = formData.get('review');\n        // Validate required fields\n        if (!name?.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Name is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!location?.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Location is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!email?.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Email is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!rating || isNaN(parseInt(rating)) || parseInt(rating) < 1 || parseInt(rating) > 5) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Valid rating (1-5) is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!review?.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Review text is required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate word count (minimum 10 words, maximum 150 words)\n        const words = review.trim().split(/\\s+/).filter((word)=>word.length > 0);\n        if (words.length < 10) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Review must be at least 10 words long'\n            }, {\n                status: 400\n            });\n        }\n        if (words.length > 150) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Review must not exceed 150 words'\n            }, {\n                status: 400\n            });\n        }\n        // Prepare review data\n        const reviewData = {\n            name: name.trim(),\n            location: location.trim(),\n            email: email.trim(),\n            rating: parseInt(rating),\n            review: review.trim(),\n            locale: formData.get('locale') || 'en',\n            submittedAt: formData.get('submittedAt') || new Date().toISOString(),\n            status: 'pending'\n        };\n        // Store review using persistent storage\n        const savedReview = await (0,_lib_reviewStorage__WEBPACK_IMPORTED_MODULE_1__.addReview)(reviewData);\n        // Send email notification to admin about new review\n        try {\n            const reviewNotificationData = {\n                customerName: reviewData.name,\n                vehicleTitle: reviewData.vehicleTitle,\n                rating: reviewData.rating,\n                review: reviewData.review,\n                reviewDate: new Date(reviewData.submittedAt).toLocaleDateString()\n            };\n            await _lib_resendService__WEBPACK_IMPORTED_MODULE_2__.emailService.sendReviewNotificationToAdmin(reviewNotificationData);\n            console.log('Review notification email sent to admin');\n        } catch (emailError) {\n            console.error('Failed to send review notification email:', emailError);\n        // Don't fail the review submission if email fails\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Review submitted successfully! It will be reviewed by our team before being published.',\n            reviewId: savedReview.id\n        });\n    } catch (error) {\n        console.error('Error processing review:', error);\n        // Provide more specific error message\n        let errorMessage = 'Failed to submit review';\n        if (error instanceof Error) {\n            errorMessage = error.message;\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    const monitor = (0,_lib_monitoring__WEBPACK_IMPORTED_MODULE_5__.startPerformanceMonitoring)(request);\n    try {\n        // Check for admin authentication (supports both new and legacy methods)\n        const adminAuth = (0,_lib_adminMiddleware__WEBPACK_IMPORTED_MODULE_3__.getAdminAuth)(request);\n        if (adminAuth.isValid) {\n            // Admin access - return all reviews\n            const allReviews = await (0,_lib_reviewStorage__WEBPACK_IMPORTED_MODULE_1__.getAllReviews)();\n            monitor.finish(200);\n            const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                reviews: allReviews\n            });\n            // Add security headers\n            const securityHeaders = (0,_lib_security__WEBPACK_IMPORTED_MODULE_4__.getSecurityHeaders)();\n            Object.entries(securityHeaders).forEach(([key, value])=>{\n                response.headers.set(key, value);\n            });\n            return response;\n        }\n        // Public access - only return approved reviews\n        const approvedReviews = await (0,_lib_reviewStorage__WEBPACK_IMPORTED_MODULE_1__.getApprovedReviews)();\n        monitor.finish(200);\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            reviews: approvedReviews\n        });\n        // Add security headers\n        const securityHeaders = (0,_lib_security__WEBPACK_IMPORTED_MODULE_4__.getSecurityHeaders)();\n        Object.entries(securityHeaders).forEach(([key, value])=>{\n            response.headers.set(key, value);\n        });\n        return response;\n    } catch (error) {\n        (0,_lib_monitoring__WEBPACK_IMPORTED_MODULE_5__.logError)(error instanceof Error ? error : 'Unknown error', {\n            endpoint: '/api/reviews',\n            method: 'GET',\n            request,\n            additionalContext: {\n                action: 'fetch_reviews'\n            }\n        });\n        monitor.finish(500, error instanceof Error ? error.message : 'Unknown error');\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to fetch reviews'\n        }, {\n            status: 500\n        });\n        // Add security headers even for errors\n        const securityHeaders = (0,_lib_security__WEBPACK_IMPORTED_MODULE_4__.getSecurityHeaders)();\n        Object.entries(securityHeaders).forEach(([key, value])=>{\n            response.headers.set(key, value);\n        });\n        return response;\n    }\n}\n// Admin endpoint to approve/reject reviews\nasync function PATCH(request) {\n    try {\n        const { reviewId, status, adminKey } = await request.json();\n        // Admin authentication using environment variable\n        const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123'; // Fallback for development\n        if (adminKey !== validAdminKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Get review data before updating (for email notification)\n        const review = await (0,_lib_reviewStorage__WEBPACK_IMPORTED_MODULE_1__.getReviewById)(reviewId);\n        if (!review) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Review not found'\n            }, {\n                status: 404\n            });\n        }\n        // Update review status using persistent storage\n        const success = await (0,_lib_reviewStorage__WEBPACK_IMPORTED_MODULE_1__.updateReviewStatus)(reviewId, status);\n        if (!success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Failed to update review status'\n            }, {\n                status: 500\n            });\n        }\n        // Send email notification if review is approved\n        if (status === 'approved' && review.email) {\n            try {\n                const reviewNotificationData = {\n                    customerName: review.name,\n                    vehicleTitle: review.vehicleTitle,\n                    rating: review.rating,\n                    review: review.review,\n                    reviewDate: new Date(review.submittedAt).toLocaleDateString(),\n                    isApproval: true\n                };\n                await _lib_resendService__WEBPACK_IMPORTED_MODULE_2__.emailService.sendReviewApprovalNotification(review.email, reviewNotificationData);\n                console.log('Review approval email sent to customer');\n            } catch (emailError) {\n                console.error('Failed to send review approval email:', emailError);\n            // Don't fail the approval process if email fails\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Review ${status} successfully`\n        });\n    } catch (error) {\n        console.error('Error moderating review:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to moderate review'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/reviews/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/adminMiddleware.ts":
/*!************************************!*\
  !*** ./src/lib/adminMiddleware.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAdminAuth: () => (/* binding */ getAdminAuth),\n/* harmony export */   getAdminFromRequest: () => (/* binding */ getAdminFromRequest),\n/* harmony export */   verifyLegacyAdminKey: () => (/* binding */ verifyLegacyAdminKey),\n/* harmony export */   withAdminAuth: () => (/* binding */ withAdminAuth)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\n/**\n * Middleware to verify admin authentication for API routes\n */ function withAdminAuth(handler) {\n    return async (request, context)=>{\n        try {\n            // Get authentication from headers or cookies\n            const authHeader = request.headers.get('authorization');\n            const sessionId = request.cookies.get('admin_session')?.value;\n            // Verify authentication\n            const authResult = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyAdminAuth)(authHeader, sessionId);\n            if (!authResult.isValid) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: authResult.message\n                }, {\n                    status: 401\n                });\n            }\n            // Add admin info to request headers for the handler\n            const requestWithAuth = new next_server__WEBPACK_IMPORTED_MODULE_0__.NextRequest(request.url, {\n                method: request.method,\n                headers: {\n                    ...Object.fromEntries(request.headers.entries()),\n                    'x-admin-id': authResult.adminId || 'admin',\n                    'x-admin-authenticated': 'true'\n                },\n                body: request.body\n            });\n            return handler(requestWithAuth, context);\n        } catch (error) {\n            console.error('Admin middleware error:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Authentication error'\n            }, {\n                status: 500\n            });\n        }\n    };\n}\n/**\n * Extract admin authentication from request\n */ function getAdminFromRequest(request) {\n    const adminId = request.headers.get('x-admin-id') || 'admin';\n    const isAuthenticated = request.headers.get('x-admin-authenticated') === 'true';\n    return {\n        adminId,\n        isAuthenticated\n    };\n}\n/**\n * Verify admin authentication for legacy API routes that use adminKey\n */ function verifyLegacyAdminKey(adminKey) {\n    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';\n    return adminKey === validAdminKey;\n}\n/**\n * Get admin authentication from request (supports both new and legacy methods)\n */ function getAdminAuth(request, body) {\n    // Try new authentication method first\n    const authHeader = request.headers.get('authorization');\n    const sessionId = request.cookies.get('admin_session')?.value;\n    const authResult = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyAdminAuth)(authHeader, sessionId);\n    if (authResult.isValid) {\n        return {\n            isValid: true,\n            adminId: authResult.adminId,\n            method: 'token/session'\n        };\n    }\n    // Fall back to legacy adminKey method\n    const adminKey = body?.adminKey || request.nextUrl.searchParams.get('adminKey');\n    if (adminKey && verifyLegacyAdminKey(adminKey)) {\n        return {\n            isValid: true,\n            adminId: 'admin',\n            method: 'legacy'\n        };\n    }\n    return {\n        isValid: false,\n        method: 'none'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/adminMiddleware.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateAdmin: () => (/* binding */ authenticateAdmin),\n/* harmony export */   checkAuthRateLimit: () => (/* binding */ checkAuthRateLimit),\n/* harmony export */   createAdminSession: () => (/* binding */ createAdminSession),\n/* harmony export */   destroyAdminSession: () => (/* binding */ destroyAdminSession),\n/* harmony export */   generateAdminToken: () => (/* binding */ generateAdminToken),\n/* harmony export */   getAdminPasswordHash: () => (/* binding */ getAdminPasswordHash),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   resetAuthRateLimit: () => (/* binding */ resetAuthRateLimit),\n/* harmony export */   validateAdminSession: () => (/* binding */ validateAdminSession),\n/* harmony export */   verifyAdminAuth: () => (/* binding */ verifyAdminAuth),\n/* harmony export */   verifyAdminToken: () => (/* binding */ verifyAdminToken),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Security configuration\nconst SALT_ROUNDS = 12;\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';\nconst JWT_EXPIRES_IN = '24h';\nconst SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n// In-memory session store (replace with Redis in production)\nconst activeSessions = new Map();\n/**\n * Hash a password using bcrypt\n */ async function hashPassword(password) {\n    try {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, SALT_ROUNDS);\n    } catch (error) {\n        console.error('Error hashing password:', error);\n        throw new Error('Failed to hash password');\n    }\n}\n/**\n * Verify a password against its hash\n */ async function verifyPassword(password, hash) {\n    try {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hash);\n    } catch (error) {\n        console.error('Error verifying password:', error);\n        return false;\n    }\n}\n/**\n * Generate a JWT token for admin authentication\n */ function generateAdminToken(adminId = 'admin') {\n    try {\n        const payload = {\n            id: adminId,\n            isAdmin: true,\n            iat: Math.floor(Date.now() / 1000)\n        };\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN\n        });\n    } catch (error) {\n        console.error('Error generating token:', error);\n        throw new Error('Failed to generate authentication token');\n    }\n}\n/**\n * Verify and decode a JWT token\n */ function verifyAdminToken(token) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        if (decoded.isAdmin) {\n            return {\n                id: decoded.id,\n                isAdmin: decoded.isAdmin\n            };\n        }\n        return null;\n    } catch (error) {\n        // Token is invalid or expired\n        return null;\n    }\n}\n/**\n * Create a new admin session\n */ function createAdminSession(adminId = 'admin') {\n    const sessionId = generateSessionId();\n    const now = Date.now();\n    const session = {\n        id: adminId,\n        isAdmin: true,\n        createdAt: now,\n        expiresAt: now + SESSION_TIMEOUT,\n        lastActivity: now\n    };\n    activeSessions.set(sessionId, session);\n    // Clean up expired sessions\n    cleanupExpiredSessions();\n    return sessionId;\n}\n/**\n * Validate an admin session\n */ function validateAdminSession(sessionId) {\n    const session = activeSessions.get(sessionId);\n    if (!session) {\n        return null;\n    }\n    const now = Date.now();\n    // Check if session has expired\n    if (now > session.expiresAt) {\n        activeSessions.delete(sessionId);\n        return null;\n    }\n    // Update last activity\n    session.lastActivity = now;\n    activeSessions.set(sessionId, session);\n    return session;\n}\n/**\n * Destroy an admin session\n */ function destroyAdminSession(sessionId) {\n    return activeSessions.delete(sessionId);\n}\n/**\n * Generate a secure session ID\n */ function generateSessionId() {\n    return `admin_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;\n}\n/**\n * Clean up expired sessions\n */ function cleanupExpiredSessions() {\n    const now = Date.now();\n    for (const [sessionId, session] of activeSessions.entries()){\n        if (now > session.expiresAt) {\n            activeSessions.delete(sessionId);\n        }\n    }\n}\n/**\n * Get admin password hash from environment\n * In production, this should be stored in a secure database\n */ function getAdminPasswordHash() {\n    // For backward compatibility, check if password is already hashed\n    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';\n    // If it starts with $2a$, $2b$, or $2y$, it's already a bcrypt hash\n    if (adminPassword.startsWith('$2a$') || adminPassword.startsWith('$2b$') || adminPassword.startsWith('$2y$')) {\n        return adminPassword;\n    }\n    // For development/migration: return the plain password (will be handled in auth route)\n    return adminPassword;\n}\n/**\n * Secure admin authentication\n */ async function authenticateAdmin(password) {\n    try {\n        const adminPasswordHash = getAdminPasswordHash();\n        let isValid = false;\n        // Check if stored password is hashed or plain text\n        if (adminPasswordHash.startsWith('$2a$') || adminPasswordHash.startsWith('$2b$') || adminPasswordHash.startsWith('$2y$')) {\n            // Password is hashed, use bcrypt comparison\n            isValid = await verifyPassword(password, adminPasswordHash);\n        } else {\n            // Password is plain text (development/migration), use direct comparison\n            isValid = password === adminPasswordHash;\n        }\n        if (isValid) {\n            const token = generateAdminToken();\n            const sessionId = createAdminSession();\n            return {\n                success: true,\n                token,\n                sessionId,\n                message: 'Authentication successful'\n            };\n        } else {\n            return {\n                success: false,\n                message: 'Invalid credentials'\n            };\n        }\n    } catch (error) {\n        console.error('Authentication error:', error);\n        return {\n            success: false,\n            message: 'Authentication failed'\n        };\n    }\n}\n/**\n * Middleware to verify admin authentication\n */ function verifyAdminAuth(authHeader, sessionId) {\n    // Check JWT token\n    if (authHeader && authHeader.startsWith('Bearer ')) {\n        const token = authHeader.substring(7);\n        const decoded = verifyAdminToken(token);\n        if (decoded) {\n            return {\n                isValid: true,\n                adminId: decoded.id,\n                message: 'Token authentication successful'\n            };\n        }\n    }\n    // Check session ID\n    if (sessionId) {\n        const session = validateAdminSession(sessionId);\n        if (session) {\n            return {\n                isValid: true,\n                adminId: session.id,\n                message: 'Session authentication successful'\n            };\n        }\n    }\n    return {\n        isValid: false,\n        message: 'Authentication required'\n    };\n}\n/**\n * Rate limiting for authentication attempts\n */ const authAttempts = new Map();\nconst MAX_AUTH_ATTEMPTS = 5;\nconst AUTH_LOCKOUT_TIME = 15 * 60 * 1000; // 15 minutes\nfunction checkAuthRateLimit(ip) {\n    const now = Date.now();\n    const attempts = authAttempts.get(ip);\n    if (!attempts) {\n        authAttempts.set(ip, {\n            count: 1,\n            lastAttempt: now\n        });\n        return {\n            allowed: true,\n            remainingAttempts: MAX_AUTH_ATTEMPTS - 1\n        };\n    }\n    // Reset if lockout time has passed\n    if (now - attempts.lastAttempt > AUTH_LOCKOUT_TIME) {\n        authAttempts.set(ip, {\n            count: 1,\n            lastAttempt: now\n        });\n        return {\n            allowed: true,\n            remainingAttempts: MAX_AUTH_ATTEMPTS - 1\n        };\n    }\n    // Check if max attempts exceeded\n    if (attempts.count >= MAX_AUTH_ATTEMPTS) {\n        const lockoutTime = AUTH_LOCKOUT_TIME - (now - attempts.lastAttempt);\n        return {\n            allowed: false,\n            remainingAttempts: 0,\n            lockoutTime\n        };\n    }\n    // Increment attempt count\n    attempts.count++;\n    attempts.lastAttempt = now;\n    authAttempts.set(ip, attempts);\n    return {\n        allowed: true,\n        remainingAttempts: MAX_AUTH_ATTEMPTS - attempts.count\n    };\n}\nfunction resetAuthRateLimit(ip) {\n    authAttempts.delete(ip);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/emailTemplates.ts":
/*!***********************************!*\
  !*** ./src/lib/emailTemplates.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailTemplates: () => (/* binding */ EmailTemplates)\n/* harmony export */ });\n// Base email styles\nconst emailStyles = `\n  <style>\n    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }\n    .container { max-width: 600px; margin: 0 auto; background-color: white; }\n    .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; }\n    .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }\n    .content { padding: 30px; }\n    .vehicle-card { border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin: 20px 0; }\n    .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }\n    .footer { background-color: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }\n    .highlight { background-color: #eff6ff; padding: 15px; border-radius: 6px; margin: 15px 0; }\n    .status-badge { display: inline-block; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; }\n    .status-confirmed { background-color: #dcfce7; color: #166534; }\n    .divider { height: 1px; background-color: #e5e7eb; margin: 20px 0; }\n  </style>\n`;\nclass EmailTemplates {\n    /**\n   * Generate Order Confirmation HTML\n   */ static generateOrderConfirmationHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Order Confirmation - ${data.orderNumber}</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <!-- Header -->\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Your trusted partner for quality vehicles from Japan to Ghana</p>\n          </div>\n\n          <!-- Content -->\n          <div class=\"content\">\n            <h1>Order Confirmation</h1>\n            <p>Dear ${data.customerName},</p>\n            <p>Thank you for your order! We're excited to help you get your new vehicle.</p>\n\n            <div class=\"highlight\">\n              <h3>Order Details</h3>\n              <p><strong>Order Number:</strong> ${data.orderNumber}</p>\n              <p><strong>Order Date:</strong> ${data.orderDate}</p>\n              <p><strong>Status:</strong> <span class=\"status-badge status-confirmed\">Confirmed</span></p>\n            </div>\n\n            <!-- Vehicle Details -->\n            <div class=\"vehicle-card\">\n              <h3>Vehicle Information</h3>\n              <img src=\"${data.vehicle.image}\" alt=\"${data.vehicle.title}\" style=\"width: 100%; max-width: 300px; height: 200px; object-fit: cover; border-radius: 6px; margin-bottom: 15px;\">\n              <h4>${data.vehicle.title}</h4>\n              <p><strong>Price:</strong> ${data.vehicle.price}</p>\n            </div>\n\n            <!-- Shipping Information -->\n            <div class=\"highlight\">\n              <h3>Shipping Address</h3>\n              <p>\n                ${data.shippingAddress.street}<br>\n                ${data.shippingAddress.city}, ${data.shippingAddress.state}<br>\n                ${data.shippingAddress.country} ${data.shippingAddress.postalCode}\n              </p>\n              <p><strong>Estimated Delivery:</strong> ${data.estimatedDelivery}</p>\n            </div>\n\n            <!-- Order Summary -->\n            <div class=\"divider\"></div>\n            <div style=\"text-align: right;\">\n              <h3>Order Total: ${data.total}</h3>\n            </div>\n\n            <!-- Next Steps -->\n            <div class=\"highlight\">\n              <h3>What's Next?</h3>\n              <ul>\n                <li>We'll prepare your vehicle for shipping</li>\n                <li>You'll receive tracking information once shipped</li>\n                <li>Our team will contact you for any updates</li>\n              </ul>\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/tracking?order=${data.orderNumber}\" class=\"button\">Track Your Order</a>\n            </div>\n\n            <p>If you have any questions, please don't hesitate to contact us:</p>\n            <ul>\n              <li>📧 Email: <EMAIL></li>\n              <li>📱 WhatsApp: +233245375692</li>\n              <li>📍 Location: Kumasi, Ghana</li>\n            </ul>\n          </div>\n\n          <!-- Footer -->\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n            <p>This email was sent to confirm your order. Please keep this for your records.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Order Confirmation Text\n   */ static generateOrderConfirmationText(data) {\n        return `\nORDER CONFIRMATION - EBAM Motors\n\nDear ${data.customerName},\n\nThank you for your order! We're excited to help you get your new vehicle.\n\nORDER DETAILS:\n- Order Number: ${data.orderNumber}\n- Order Date: ${data.orderDate}\n- Status: Confirmed\n\nVEHICLE:\n- ${data.vehicle.title}\n- Price: ${data.vehicle.price}\n\nSHIPPING ADDRESS:\n${data.shippingAddress.street}\n${data.shippingAddress.city}, ${data.shippingAddress.state}\n${data.shippingAddress.country} ${data.shippingAddress.postalCode}\n\nEstimated Delivery: ${data.estimatedDelivery}\n\nORDER TOTAL: ${data.total}\n\nWHAT'S NEXT:\n- We'll prepare your vehicle for shipping\n- You'll receive tracking information once shipped\n- Our team will contact you for any updates\n\nTrack your order: https://yourdomain.com/tracking?order=${data.orderNumber}\n\nCONTACT US:\n- Email: <EMAIL>\n- WhatsApp: +233245375692\n- Location: Kumasi, Ghana\n\nThank you for choosing EBAM Motors!\n© 2024 EBAM Motors. All rights reserved.\n    `;\n    }\n    /**\n   * Generate Review Notification HTML for Admin\n   */ static generateReviewNotificationHTML(data) {\n        const stars = '⭐'.repeat(data.rating) + '☆'.repeat(5 - data.rating);\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>New Review Submitted</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors Admin</div>\n            <p>New Review Notification</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>New Review Submitted</h1>\n            \n            <div class=\"highlight\">\n              <h3>Review Details</h3>\n              <p><strong>Customer:</strong> ${data.customerName}</p>\n              <p><strong>Vehicle:</strong> ${data.vehicleTitle}</p>\n              <p><strong>Rating:</strong> ${stars} (${data.rating}/5)</p>\n              <p><strong>Date:</strong> ${data.reviewDate}</p>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>Review Content</h3>\n              <p>\"${data.review}\"</p>\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/admin/reviews\" class=\"button\">Review & Approve</a>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <p>EBAM Motors Admin Panel</p>\n            <p>Please review and approve/reject this review in the admin panel.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Review Approval HTML for Customer\n   */ static generateReviewApprovalHTML(data) {\n        const stars = '⭐'.repeat(data.rating) + '☆'.repeat(5 - data.rating);\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Review Approved</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Thank you for your feedback!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>Your Review Has Been Approved!</h1>\n            <p>Dear ${data.customerName},</p>\n            <p>Thank you for taking the time to review your experience with us. Your review has been approved and is now live on our website!</p>\n\n            <div class=\"highlight\">\n              <h3>Your Review</h3>\n              <p><strong>Vehicle:</strong> ${data.vehicleTitle}</p>\n              <p><strong>Rating:</strong> ${stars} (${data.rating}/5)</p>\n              <p><strong>Review:</strong> \"${data.review}\"</p>\n            </div>\n\n            <p>Your feedback helps other customers make informed decisions and helps us improve our services.</p>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/reviews\" class=\"button\">View All Reviews</a>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Contact Form Admin Notification HTML\n   */ static generateContactFormAdminHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>New Contact Form Submission</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors Admin</div>\n            <p>New Contact Form Submission</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>New Contact Form Submission</h1>\n            \n            <div class=\"highlight\">\n              <h3>Contact Details</h3>\n              <p><strong>Name:</strong> ${data.name}</p>\n              <p><strong>Email:</strong> ${data.email}</p>\n              <p><strong>Subject:</strong> ${data.subject}</p>\n              <p><strong>Submitted:</strong> ${data.submissionDate}</p>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>Message</h3>\n              <p>${data.message.replace(/\\n/g, '<br>')}</p>\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"mailto:${data.email}?subject=Re: ${data.subject}\" class=\"button\">Reply to Customer</a>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <p>EBAM Motors Admin Panel</p>\n            <p>Please respond to this customer inquiry promptly.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Abandoned Cart Follow-up HTML\n   */ static generateAbandonedCartHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Complete Your Purchase</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Don't miss out on your perfect vehicle!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>Complete Your Purchase</h1>\n            <p>Hi ${data.customerName},</p>\n            <p>We noticed you were interested in some amazing vehicles but didn't complete your purchase. Don't worry - we've saved your items!</p>\n\n            <div class=\"highlight\">\n              <h3>Your Saved Items</h3>\n              ${data.data?.items?.map((item)=>`\n                <div class=\"vehicle-card\">\n                  <h4>${item.title}</h4>\n                  <p><strong>Price:</strong> ${item.price}</p>\n                  <p>Quantity: ${item.quantity}</p>\n                </div>\n              `).join('') || '<p>Your selected vehicles are waiting for you!</p>'}\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/stock\" class=\"button\">Complete Your Purchase</a>\n            </div>\n\n            <div class=\"highlight\">\n              <h3>Why Choose EBAM Motors?</h3>\n              <ul>\n                <li>✅ Quality guaranteed vehicles from Japan</li>\n                <li>✅ Competitive pricing with transparent costs</li>\n                <li>✅ Reliable shipping to Ghana and Africa</li>\n                <li>✅ Expert support throughout the process</li>\n              </ul>\n            </div>\n\n            <p>Need help deciding? Our team is here to assist you:</p>\n            <ul>\n              <li>📱 WhatsApp: +233245375692</li>\n              <li>📧 Email: <EMAIL></li>\n            </ul>\n          </div>\n\n          <div class=\"footer\">\n            <p>This offer won't last forever!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Delivery Update HTML\n   */ static generateDeliveryUpdateHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Delivery Update</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Your vehicle is on its way!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>Delivery Update</h1>\n            <p>Hi ${data.customerName},</p>\n            <p>Great news! We have an update on your vehicle delivery.</p>\n\n            <div class=\"highlight\">\n              <h3>Delivery Status</h3>\n              <p><strong>Current Status:</strong> ${data.data?.status || 'In Transit'}</p>\n              <p><strong>Location:</strong> ${data.data?.location || 'En route to destination'}</p>\n              <p><strong>Estimated Arrival:</strong> ${data.data?.estimatedArrival || 'To be confirmed'}</p>\n            </div>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/tracking?order=${data.data?.orderId}\" class=\"button\">Track Your Order</a>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>What to Expect Next</h3>\n              <ul>\n                <li>We'll notify you 24 hours before delivery</li>\n                <li>Our delivery team will contact you directly</li>\n                <li>Ensure someone is available to receive the vehicle</li>\n                <li>Have your ID and order confirmation ready</li>\n              </ul>\n            </div>\n          </div>\n\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Feedback Request HTML\n   */ static generateFeedbackRequestHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>How was your experience?</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>We'd love to hear from you!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>How Was Your Experience?</h1>\n            <p>Hi ${data.customerName},</p>\n            <p>We hope you're enjoying your new vehicle! Your feedback helps us improve our services and helps other customers make informed decisions.</p>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/reviews\" class=\"button\">Leave a Review</a>\n            </div>\n\n            <div class=\"highlight\">\n              <h3>Share Your Experience</h3>\n              <p>Tell us about:</p>\n              <ul>\n                <li>🚗 Vehicle quality and condition</li>\n                <li>📦 Shipping and delivery experience</li>\n                <li>👥 Customer service quality</li>\n                <li>💰 Value for money</li>\n                <li>🌟 Overall satisfaction</li>\n              </ul>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>Why Your Review Matters</h3>\n              <ul>\n                <li>Helps other customers make confident decisions</li>\n                <li>Helps us improve our services</li>\n                <li>Builds trust in our community</li>\n                <li>Takes less than 2 minutes to complete</li>\n              </ul>\n            </div>\n\n            <p>As a thank you, customers who leave reviews get priority support and exclusive offers!</p>\n          </div>\n\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate Contact Form Customer Confirmation HTML\n   */ static generateContactFormCustomerHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Thank you for contacting us</title>\n        ${emailStyles}\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Thank you for reaching out!</p>\n          </div>\n\n          <div class=\"content\">\n            <h1>Thank You for Contacting Us!</h1>\n            <p>Dear ${data.name},</p>\n            <p>We've received your message and appreciate you taking the time to contact us. Our team will review your inquiry and respond within 24 hours.</p>\n\n            <div class=\"highlight\">\n              <h3>Your Message Summary</h3>\n              <p><strong>Subject:</strong> ${data.subject}</p>\n              <p><strong>Submitted:</strong> ${data.submissionDate}</p>\n              <p><strong>Reference ID:</strong> #${Date.now().toString().slice(-6)}</p>\n            </div>\n\n            <div class=\"vehicle-card\">\n              <h3>What to Expect Next</h3>\n              <ul>\n                <li>Our team will review your inquiry</li>\n                <li>You'll receive a response within 24 hours</li>\n                <li>For urgent matters, contact us on WhatsApp</li>\n              </ul>\n            </div>\n\n            <p>In the meantime, feel free to:</p>\n            <ul>\n              <li>Browse our latest vehicle inventory</li>\n              <li>Check out customer reviews</li>\n              <li>Learn more about our services</li>\n            </ul>\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/stock\" class=\"button\">Browse Vehicles</a>\n            </div>\n\n            <p><strong>Need immediate assistance?</strong></p>\n            <ul>\n              <li>📱 WhatsApp: +233245375692</li>\n              <li>📧 Email: <EMAIL></li>\n              <li>📍 Location: Kumasi, Ghana</li>\n            </ul>\n          </div>\n\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/emailTemplates.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/monitoring.ts":
/*!*******************************!*\
  !*** ./src/lib/monitoring.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkDatabaseHealth: () => (/* binding */ checkDatabaseHealth),\n/* harmony export */   cleanupOldData: () => (/* binding */ cleanupOldData),\n/* harmony export */   getHealthStatus: () => (/* binding */ getHealthStatus),\n/* harmony export */   getMemoryUsage: () => (/* binding */ getMemoryUsage),\n/* harmony export */   getPerformanceStats: () => (/* binding */ getPerformanceStats),\n/* harmony export */   getRecentErrors: () => (/* binding */ getRecentErrors),\n/* harmony export */   logError: () => (/* binding */ logError),\n/* harmony export */   startPerformanceMonitoring: () => (/* binding */ startPerformanceMonitoring)\n/* harmony export */ });\n// In-memory storage (replace with database/external service in production)\nconst performanceMetrics = [];\nconst errorLogs = [];\nconst MAX_STORED_METRICS = 1000;\nconst MAX_STORED_ERRORS = 500;\n/**\n * Start performance monitoring for a request\n */ function startPerformanceMonitoring(request) {\n    const startTime = Date.now();\n    const endpoint = new URL(request.url).pathname;\n    const method = request.method;\n    return {\n        endpoint,\n        method,\n        startTime,\n        finish: (status, error)=>{\n            const duration = Date.now() - startTime;\n            const userAgent = request.headers.get('user-agent') || undefined;\n            const ip = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined;\n            const metric = {\n                timestamp: Date.now(),\n                endpoint,\n                method,\n                duration,\n                status,\n                userAgent,\n                ip,\n                error\n            };\n            // Store metric\n            performanceMetrics.push(metric);\n            // Keep only recent metrics\n            if (performanceMetrics.length > MAX_STORED_METRICS) {\n                performanceMetrics.splice(0, performanceMetrics.length - MAX_STORED_METRICS);\n            }\n            // Log slow requests\n            if (duration > 5000) {\n                console.warn(`[PERFORMANCE] Slow request detected: ${method} ${endpoint} took ${duration}ms`);\n            }\n            // Log errors\n            if (status >= 400) {\n                console.error(`[ERROR] ${method} ${endpoint} returned ${status}${error ? `: ${error}` : ''}`);\n            }\n        }\n    };\n}\n/**\n * Log an error with context\n */ function logError(error, context) {\n    const errorMessage = error instanceof Error ? error.message : error;\n    const stack = error instanceof Error ? error.stack : undefined;\n    const errorLog = {\n        timestamp: Date.now(),\n        error: errorMessage,\n        stack,\n        endpoint: context.endpoint || 'unknown',\n        method: context.method || 'unknown',\n        userAgent: context.request?.headers.get('user-agent') || undefined,\n        ip: context.request?.headers.get('x-forwarded-for') || context.request?.headers.get('x-real-ip') || undefined,\n        context: context.additionalContext\n    };\n    // Store error\n    errorLogs.push(errorLog);\n    // Keep only recent errors\n    if (errorLogs.length > MAX_STORED_ERRORS) {\n        errorLogs.splice(0, errorLogs.length - MAX_STORED_ERRORS);\n    }\n    // Console log for immediate visibility\n    console.error(`[ERROR] ${errorMessage}`, {\n        endpoint: errorLog.endpoint,\n        method: errorLog.method,\n        ip: errorLog.ip,\n        context: errorLog.context\n    });\n}\n/**\n * Get performance statistics\n */ function getPerformanceStats(timeRange = 60 * 60 * 1000) {\n    const now = Date.now();\n    const cutoff = now - timeRange;\n    const recentMetrics = performanceMetrics.filter((m)=>m.timestamp > cutoff);\n    if (recentMetrics.length === 0) {\n        return {\n            totalRequests: 0,\n            averageResponseTime: 0,\n            errorRate: 0,\n            slowRequests: 0,\n            endpointStats: {}\n        };\n    }\n    const totalRequests = recentMetrics.length;\n    const averageResponseTime = recentMetrics.reduce((sum, m)=>sum + m.duration, 0) / totalRequests;\n    const errorCount = recentMetrics.filter((m)=>m.status >= 400).length;\n    const errorRate = errorCount / totalRequests * 100;\n    const slowRequests = recentMetrics.filter((m)=>m.duration > 5000).length;\n    // Group by endpoint\n    const endpointStats = {};\n    recentMetrics.forEach((metric)=>{\n        if (!endpointStats[metric.endpoint]) {\n            endpointStats[metric.endpoint] = {\n                requests: 0,\n                averageTime: 0,\n                errors: 0,\n                slowRequests: 0\n            };\n        }\n        const stats = endpointStats[metric.endpoint];\n        stats.requests++;\n        stats.averageTime = (stats.averageTime * (stats.requests - 1) + metric.duration) / stats.requests;\n        if (metric.status >= 400) stats.errors++;\n        if (metric.duration > 5000) stats.slowRequests++;\n    });\n    return {\n        totalRequests,\n        averageResponseTime: Math.round(averageResponseTime),\n        errorRate: Math.round(errorRate * 100) / 100,\n        slowRequests,\n        endpointStats\n    };\n}\n/**\n * Get recent errors\n */ function getRecentErrors(limit = 50) {\n    return errorLogs.slice(-limit).reverse() // Most recent first\n    .map((error)=>({\n            timestamp: new Date(error.timestamp).toISOString(),\n            error: error.error,\n            endpoint: error.endpoint,\n            method: error.method,\n            ip: error.ip,\n            context: error.context\n        }));\n}\n/**\n * Health check function\n */ function getHealthStatus() {\n    const stats = getPerformanceStats(5 * 60 * 1000); // Last 5 minutes\n    const recentErrors = getRecentErrors(10);\n    // Determine health status\n    let status = 'healthy';\n    const issues = [];\n    if (stats.errorRate > 10) {\n        status = 'unhealthy';\n        issues.push(`High error rate: ${stats.errorRate}%`);\n    } else if (stats.errorRate > 5) {\n        status = 'degraded';\n        issues.push(`Elevated error rate: ${stats.errorRate}%`);\n    }\n    if (stats.averageResponseTime > 10000) {\n        status = 'unhealthy';\n        issues.push(`Very slow response time: ${stats.averageResponseTime}ms`);\n    } else if (stats.averageResponseTime > 5000) {\n        if (status === 'healthy') status = 'degraded';\n        issues.push(`Slow response time: ${stats.averageResponseTime}ms`);\n    }\n    if (stats.slowRequests > stats.totalRequests * 0.1) {\n        if (status === 'healthy') status = 'degraded';\n        issues.push(`High number of slow requests: ${stats.slowRequests}`);\n    }\n    return {\n        status,\n        timestamp: new Date().toISOString(),\n        uptime: process.uptime(),\n        issues,\n        stats: {\n            requests: stats.totalRequests,\n            averageResponseTime: stats.averageResponseTime,\n            errorRate: stats.errorRate,\n            slowRequests: stats.slowRequests\n        },\n        recentErrors: recentErrors.slice(0, 3) // Only show 3 most recent errors\n    };\n}\n/**\n * Database health check\n */ async function checkDatabaseHealth() {\n    try {\n        const start = Date.now();\n        // Simple database query to check connectivity\n        // This should be replaced with actual database health check\n        const { sql } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/ws\"), __webpack_require__.e(\"vendor-chunks/@vercel\"), __webpack_require__.e(\"vendor-chunks/node-gyp-build\"), __webpack_require__.e(\"vendor-chunks/bufferutil\"), __webpack_require__.e(\"vendor-chunks/@neondatabase\"), __webpack_require__.e(\"_66e9\")]).then(__webpack_require__.bind(__webpack_require__, /*! @vercel/postgres */ \"(rsc)/./node_modules/@vercel/postgres/dist/index-node.js\"));\n        await sql`SELECT 1 as health_check`;\n        const latency = Date.now() - start;\n        return {\n            healthy: true,\n            latency\n        };\n    } catch (error) {\n        return {\n            healthy: false,\n            error: error instanceof Error ? error.message : 'Unknown database error'\n        };\n    }\n}\n/**\n * Memory usage monitoring\n */ function getMemoryUsage() {\n    const usage = process.memoryUsage();\n    return {\n        rss: Math.round(usage.rss / 1024 / 1024),\n        heapTotal: Math.round(usage.heapTotal / 1024 / 1024),\n        heapUsed: Math.round(usage.heapUsed / 1024 / 1024),\n        external: Math.round(usage.external / 1024 / 1024),\n        arrayBuffers: Math.round(usage.arrayBuffers / 1024 / 1024)\n    };\n}\n/**\n * Clean up old metrics and errors\n */ function cleanupOldData() {\n    const oneHourAgo = Date.now() - 60 * 60 * 1000;\n    // Remove old performance metrics\n    const validMetrics = performanceMetrics.filter((m)=>m.timestamp > oneHourAgo);\n    performanceMetrics.splice(0, performanceMetrics.length, ...validMetrics);\n    // Remove old error logs\n    const validErrors = errorLogs.filter((e)=>e.timestamp > oneHourAgo);\n    errorLogs.splice(0, errorLogs.length, ...validErrors);\n}\n// Clean up old data every 10 minutes\nsetInterval(cleanupOldData, 10 * 60 * 1000);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/monitoring.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/resendService.ts":
/*!**********************************!*\
  !*** ./src/lib/resendService.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EMAIL_CONFIG: () => (/* binding */ EMAIL_CONFIG),\n/* harmony export */   ResendEmailService: () => (/* binding */ ResendEmailService),\n/* harmony export */   emailService: () => (/* binding */ emailService)\n/* harmony export */ });\n/* harmony import */ var resend__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! resend */ \"(rsc)/./node_modules/resend/dist/index.mjs\");\n/* harmony import */ var _emailTemplates__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./emailTemplates */ \"(rsc)/./src/lib/emailTemplates.ts\");\n\n\n// Initialize Resend with API key\nconst resend = new resend__WEBPACK_IMPORTED_MODULE_0__.Resend(process.env.RESEND_API_KEY);\n// Email configuration\nconst EMAIL_CONFIG = {\n    from: process.env.RESEND_FROM_EMAIL || 'EBAM Motors <<EMAIL>>',\n    adminEmail: process.env.ADMIN_EMAIL || '<EMAIL>',\n    supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',\n    noReplyEmail: process.env.NO_REPLY_EMAIL || '<EMAIL>'\n};\n// Base email service class\nclass ResendEmailService {\n    constructor(){\n        this.resend = resend;\n    }\n    /**\n   * Send a generic email\n   */ async sendEmail(template) {\n        try {\n            if (!process.env.RESEND_API_KEY) {\n                console.warn('Resend API key not configured. Email not sent.');\n                return {\n                    success: false,\n                    error: 'Resend API key not configured'\n                };\n            }\n            const result = await this.resend.emails.send({\n                from: EMAIL_CONFIG.from,\n                to: template.to,\n                subject: template.subject,\n                html: template.html,\n                text: template.text\n            });\n            if (result.error) {\n                console.error('Resend email error:', result.error);\n                return {\n                    success: false,\n                    error: result.error.message\n                };\n            }\n            console.log('Email sent successfully:', result.data?.id);\n            return {\n                success: true,\n                messageId: result.data?.id\n            };\n        } catch (error) {\n            console.error('Email service error:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Send order confirmation email\n   */ async sendOrderConfirmation(customerEmail, data) {\n        const template = {\n            to: customerEmail,\n            subject: `Order Confirmation - ${data.orderNumber} | EBAM Motors`,\n            html: this.generateOrderConfirmationHTML(data),\n            text: this.generateOrderConfirmationText(data)\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    /**\n   * Send review notification to admin\n   */ async sendReviewNotificationToAdmin(data) {\n        const template = {\n            to: EMAIL_CONFIG.adminEmail,\n            subject: `New Review Submitted - ${data.vehicleTitle} | EBAM Motors`,\n            html: this.generateReviewNotificationHTML(data),\n            text: this.generateReviewNotificationText(data)\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    /**\n   * Send review approval notification to customer\n   */ async sendReviewApprovalNotification(customerEmail, data) {\n        const template = {\n            to: customerEmail,\n            subject: `Your Review Has Been Approved | EBAM Motors`,\n            html: this.generateReviewApprovalHTML(data),\n            text: this.generateReviewApprovalText(data)\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    /**\n   * Send contact form submission notification\n   */ async sendContactFormNotification(data) {\n        // Send to admin\n        const adminTemplate = {\n            to: EMAIL_CONFIG.adminEmail,\n            subject: `New Contact Form Submission - ${data.subject} | EBAM Motors`,\n            html: this.generateContactFormAdminHTML(data),\n            text: this.generateContactFormAdminText(data)\n        };\n        // Send confirmation to customer\n        const customerTemplate = {\n            to: data.email,\n            subject: `Thank you for contacting EBAM Motors`,\n            html: this.generateContactFormCustomerHTML(data),\n            text: this.generateContactFormCustomerText(data)\n        };\n        const [adminResult, customerResult] = await Promise.all([\n            this.sendEmail(adminTemplate),\n            this.sendEmail(customerTemplate)\n        ]);\n        return {\n            success: adminResult.success && customerResult.success,\n            error: adminResult.error || customerResult.error\n        };\n    }\n    /**\n   * Send admin notification\n   */ async sendAdminNotification(data) {\n        const template = {\n            to: EMAIL_CONFIG.adminEmail,\n            subject: `${data.title} | EBAM Motors Admin`,\n            html: this.generateAdminNotificationHTML(data),\n            text: this.generateAdminNotificationText(data)\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    /**\n   * Send follow-up email\n   */ async sendFollowUpEmail(data) {\n        let subject = '';\n        let html = '';\n        let text = '';\n        switch(data.type){\n            case 'abandoned_cart':\n                subject = 'Complete Your Purchase - Items Still Available | EBAM Motors';\n                html = this.generateAbandonedCartHTML(data);\n                text = this.generateAbandonedCartText(data);\n                break;\n            case 'delivery_update':\n                subject = 'Delivery Update for Your Order | EBAM Motors';\n                html = this.generateDeliveryUpdateHTML(data);\n                text = this.generateDeliveryUpdateText(data);\n                break;\n            case 'feedback_request':\n                subject = 'How was your experience with EBAM Motors?';\n                html = this.generateFeedbackRequestHTML(data);\n                text = this.generateFeedbackRequestText(data);\n                break;\n            case 'maintenance_reminder':\n                subject = 'Vehicle Maintenance Reminder | EBAM Motors';\n                html = this.generateMaintenanceReminderHTML(data);\n                text = this.generateMaintenanceReminderText(data);\n                break;\n            default:\n                return {\n                    success: false,\n                    error: 'Unknown follow-up type'\n                };\n        }\n        const template = {\n            to: data.customerEmail,\n            subject,\n            html,\n            text\n        };\n        const result = await this.sendEmail(template);\n        return result;\n    }\n    // HTML template generators using EmailTemplates class\n    generateOrderConfirmationHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateOrderConfirmationHTML(data);\n    }\n    generateOrderConfirmationText(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateOrderConfirmationText(data);\n    }\n    generateReviewNotificationHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateReviewNotificationHTML(data);\n    }\n    generateReviewNotificationText(data) {\n        return `New Review from ${data.customerName} for ${data.vehicleTitle} - Rating: ${data.rating}/5`;\n    }\n    generateReviewApprovalHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateReviewApprovalHTML(data);\n    }\n    generateReviewApprovalText(data) {\n        return `Your review for ${data.vehicleTitle} has been approved. Thank you ${data.customerName}!`;\n    }\n    generateContactFormAdminHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateContactFormAdminHTML(data);\n    }\n    generateContactFormAdminText(data) {\n        return `New Contact Form from ${data.name} (${data.email}) - Subject: ${data.subject}`;\n    }\n    generateContactFormCustomerHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateContactFormCustomerHTML(data);\n    }\n    generateContactFormCustomerText(data) {\n        return `Thank you for contacting EBAM Motors, ${data.name}. We received your message about \"${data.subject}\" and will respond within 24 hours.`;\n    }\n    generateAdminNotificationHTML(data) {\n        return `<h1>${data.title}</h1><p>${data.message}</p>`;\n    }\n    generateAdminNotificationText(data) {\n        return `${data.title}: ${data.message}`;\n    }\n    generateAbandonedCartHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateAbandonedCartHTML(data);\n    }\n    generateAbandonedCartText(data) {\n        return `Hi ${data.customerName}! You left some amazing vehicles in your cart. Complete your purchase at EBAM Motors and get them shipped to Ghana. Don't miss out!`;\n    }\n    generateDeliveryUpdateHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateDeliveryUpdateHTML(data);\n    }\n    generateDeliveryUpdateText(data) {\n        return `Delivery update for ${data.customerName}: ${data.data?.status || 'Your vehicle is on its way'}. Track your order at ebammotors.com`;\n    }\n    generateFeedbackRequestHTML(data) {\n        return _emailTemplates__WEBPACK_IMPORTED_MODULE_1__.EmailTemplates.generateFeedbackRequestHTML(data);\n    }\n    generateFeedbackRequestText(data) {\n        return `Hi ${data.customerName}! How was your experience with EBAM Motors? We'd love to hear your feedback. Leave a review at ebammotors.com/reviews`;\n    }\n    generateMaintenanceReminderHTML(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Vehicle Maintenance Reminder</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }\n          .container { max-width: 600px; margin: 0 auto; background-color: white; }\n          .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; }\n          .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; }\n          .content { padding: 30px; }\n          .highlight { background-color: #eff6ff; padding: 15px; border-radius: 6px; margin: 15px 0; }\n          .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }\n          .footer { background-color: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }\n        </style>\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <div class=\"logo\">🚗 EBAM Motors</div>\n            <p>Keep your vehicle in perfect condition</p>\n          </div>\n          <div class=\"content\">\n            <h1>Vehicle Maintenance Reminder</h1>\n            <p>Hi ${data.customerName},</p>\n            <p>It's time for your vehicle's scheduled maintenance to keep it running smoothly and safely.</p>\n            <div class=\"highlight\">\n              <h3>Recommended Maintenance</h3>\n              <p><strong>Vehicle:</strong> ${data.data?.vehicleTitle || 'Your vehicle'}</p>\n              <p><strong>Mileage:</strong> ${data.data?.currentMileage || 'Check your odometer'}</p>\n              <p><strong>Service Due:</strong> ${data.data?.serviceType || 'Regular maintenance'}</p>\n            </div>\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"https://yourdomain.com/contact\" class=\"button\">Schedule Service</a>\n            </div>\n            <p>Regular maintenance helps ensure:</p>\n            <ul>\n              <li>🔧 Optimal performance and fuel efficiency</li>\n              <li>🛡️ Safety and reliability</li>\n              <li>💰 Prevention of costly repairs</li>\n              <li>📈 Maintained resale value</li>\n            </ul>\n          </div>\n          <div class=\"footer\">\n            <p>Thank you for choosing EBAM Motors!</p>\n            <p>© 2024 EBAM Motors. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    generateMaintenanceReminderText(data) {\n        return `Hi ${data.customerName}! It's time for your vehicle's scheduled maintenance. Contact EBAM Motors to schedule service and keep your vehicle running smoothly.`;\n    }\n}\n// Export singleton instance\nconst emailService = new ResendEmailService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/resendService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/reviewStorage.ts":
/*!**********************************!*\
  !*** ./src/lib/reviewStorage.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addReview: () => (/* binding */ addReview),\n/* harmony export */   deleteReview: () => (/* binding */ deleteReview),\n/* harmony export */   getAllReviews: () => (/* binding */ getAllReviews),\n/* harmony export */   getApprovedReviews: () => (/* binding */ getApprovedReviews),\n/* harmony export */   getPendingReviews: () => (/* binding */ getPendingReviews),\n/* harmony export */   getReviewById: () => (/* binding */ getReviewById),\n/* harmony export */   getReviewsByStatus: () => (/* binding */ getReviewsByStatus),\n/* harmony export */   getStorageType: () => (/* binding */ getStorageType),\n/* harmony export */   saveAllReviews: () => (/* binding */ saveAllReviews),\n/* harmony export */   updateReviewStatus: () => (/* binding */ updateReviewStatus)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Check if we're in a serverless environment (like Vercel)\nconst isServerless = process.env.VERCEL || process.env.NETLIFY || process.env.AWS_LAMBDA_FUNCTION_NAME;\n/**\n * Get storage type for logging/debugging\n */ function getStorageType() {\n    return isServerless ? 'memory' : 'file';\n}\n// Path to store reviews data (for local development)\nconst REVIEWS_FILE_PATH = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'data', 'reviews.json');\nconst DATA_DIR = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'data');\n// In-memory storage for serverless environments\nlet memoryReviews = [];\n// Default sample reviews for initial setup\nconst DEFAULT_REVIEWS = [\n    {\n        id: 'sample-1',\n        name: 'Kwame Mensah',\n        location: 'Accra, Ghana',\n        email: '<EMAIL>',\n        rating: 5,\n        title: 'Excellent Service and Quality Vehicle',\n        review: 'I purchased a Toyota Voxy through EBAM Motors and the entire process was smooth and professional. The car arrived in excellent condition exactly as described. The team was very responsive to all my questions and made the shipping process hassle-free. Highly recommend!',\n        vehiclePurchased: 'Toyota Voxy 2015',\n        purchaseDate: '2024-01',\n        locale: 'en',\n        submittedAt: '2024-01-15T10:30:00.000Z',\n        status: 'approved',\n        images: []\n    },\n    {\n        id: 'sample-2',\n        name: 'Akosua Boateng',\n        location: 'Kumasi, Ghana',\n        email: '<EMAIL>',\n        rating: 5,\n        title: 'Professional and Trustworthy',\n        review: 'EBAM Motors helped me find the perfect Honda Fit for my daily commute. Their team was very knowledgeable about the vehicles and provided detailed information about each car. The shipping was fast and the car arrived in perfect condition. Great experience overall!',\n        vehiclePurchased: 'Honda Fit 2016',\n        purchaseDate: '2024-02',\n        locale: 'en',\n        submittedAt: '2024-02-10T14:20:00.000Z',\n        status: 'approved',\n        images: []\n    }\n];\n/**\n * Ensure the data directory exists\n */ async function ensureDataDirectory() {\n    try {\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.access(DATA_DIR);\n    } catch  {\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.mkdir(DATA_DIR, {\n            recursive: true\n        });\n    }\n}\n/**\n * Initialize reviews file with default data if it doesn't exist\n */ async function initializeReviewsFile() {\n    try {\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.access(REVIEWS_FILE_PATH);\n    } catch  {\n        // File doesn't exist, create it with default reviews\n        await ensureDataDirectory();\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(REVIEWS_FILE_PATH, JSON.stringify(DEFAULT_REVIEWS, null, 2), 'utf8');\n        console.log('✅ Reviews file initialized with sample data');\n    }\n}\n/**\n * Read all reviews from storage (file system or memory)\n */ async function getAllReviews() {\n    // In serverless environments, use memory storage\n    if (isServerless) {\n        // Initialize with default reviews if memory is empty\n        if (memoryReviews.length === 0) {\n            memoryReviews = [\n                ...DEFAULT_REVIEWS\n            ];\n            console.log('✅ Memory storage initialized with default reviews');\n        }\n        return memoryReviews;\n    }\n    // In local development, use file system\n    try {\n        await initializeReviewsFile();\n        const data = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.readFile(REVIEWS_FILE_PATH, 'utf8');\n        return JSON.parse(data);\n    } catch (error) {\n        console.error('Error reading reviews:', error);\n        // Return default reviews if file is corrupted\n        return DEFAULT_REVIEWS;\n    }\n}\n/**\n * Save all reviews to storage (file system or memory)\n */ async function saveAllReviews(reviews) {\n    // In serverless environments, use memory storage\n    if (isServerless) {\n        memoryReviews = [\n            ...reviews\n        ];\n        console.log('✅ Reviews saved to memory storage');\n        return;\n    }\n    // In local development, use file system\n    try {\n        await ensureDataDirectory();\n        await fs__WEBPACK_IMPORTED_MODULE_0__.promises.writeFile(REVIEWS_FILE_PATH, JSON.stringify(reviews, null, 2), 'utf8');\n        console.log('✅ Reviews saved to file system');\n    } catch (error) {\n        console.error('Error saving reviews:', error);\n        throw new Error('Failed to save reviews');\n    }\n}\n/**\n * Add a new review\n */ async function addReview(reviewData) {\n    const reviews = await getAllReviews();\n    const newReview = {\n        ...reviewData,\n        id: Date.now().toString() + '-' + Math.random().toString(36).substr(2, 9)\n    };\n    reviews.push(newReview);\n    await saveAllReviews(reviews);\n    console.log('✅ New review added:', {\n        id: newReview.id,\n        name: newReview.name,\n        rating: newReview.rating,\n        status: newReview.status\n    });\n    return newReview;\n}\n/**\n * Update review status (approve/reject)\n */ async function updateReviewStatus(reviewId, status) {\n    const reviews = await getAllReviews();\n    const reviewIndex = reviews.findIndex((review)=>review.id === reviewId);\n    if (reviewIndex === -1) {\n        return false;\n    }\n    reviews[reviewIndex].status = status;\n    await saveAllReviews(reviews);\n    console.log('✅ Review status updated:', {\n        id: reviewId,\n        status: status\n    });\n    return true;\n}\n/**\n * Get reviews by status\n */ async function getReviewsByStatus(status) {\n    const reviews = await getAllReviews();\n    return reviews.filter((review)=>review.status === status);\n}\n/**\n * Get approved reviews for public display\n */ async function getApprovedReviews() {\n    return getReviewsByStatus('approved');\n}\n/**\n * Get a specific review by ID\n */ async function getReviewById(reviewId) {\n    const allReviews = await getAllReviews();\n    return allReviews.find((review)=>review.id === reviewId) || null;\n}\n/**\n * Get pending reviews for admin review\n */ async function getPendingReviews() {\n    return getReviewsByStatus('pending');\n}\n/**\n * Delete a review (optional - for admin use)\n */ async function deleteReview(reviewId) {\n    const reviews = await getAllReviews();\n    const initialLength = reviews.length;\n    const filteredReviews = reviews.filter((review)=>review.id !== reviewId);\n    if (filteredReviews.length === initialLength) {\n        return false; // Review not found\n    }\n    await saveAllReviews(filteredReviews);\n    console.log('✅ Review deleted:', {\n        id: reviewId\n    });\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/reviewStorage.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/security.ts":
/*!*****************************!*\
  !*** ./src/lib/security.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkRateLimit: () => (/* binding */ checkRateLimit),\n/* harmony export */   cleanupRateLimits: () => (/* binding */ cleanupRateLimits),\n/* harmony export */   containsSuspiciousContent: () => (/* binding */ containsSuspiciousContent),\n/* harmony export */   generateSecureToken: () => (/* binding */ generateSecureToken),\n/* harmony export */   getClientIP: () => (/* binding */ getClientIP),\n/* harmony export */   getSecurityHeaders: () => (/* binding */ getSecurityHeaders),\n/* harmony export */   isSuspiciousRequest: () => (/* binding */ isSuspiciousRequest),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPhone: () => (/* binding */ isValidPhone),\n/* harmony export */   logSecurityEvent: () => (/* binding */ logSecurityEvent),\n/* harmony export */   resetRateLimit: () => (/* binding */ resetRateLimit),\n/* harmony export */   sanitizeInput: () => (/* binding */ sanitizeInput),\n/* harmony export */   validateFileUpload: () => (/* binding */ validateFileUpload)\n/* harmony export */ });\n// Rate limiting configuration\nconst RATE_LIMITS = {\n    auth: {\n        maxAttempts: 5,\n        windowMs: 15 * 60 * 1000\n    },\n    api: {\n        maxRequests: 100,\n        windowMs: 60 * 1000\n    },\n    contact: {\n        maxSubmissions: 3,\n        windowMs: 60 * 60 * 1000\n    },\n    review: {\n        maxSubmissions: 2,\n        windowMs: 60 * 60 * 1000\n    }\n};\n// In-memory rate limit store (use Redis in production)\nconst rateLimitStore = new Map();\n/**\n * Generic rate limiter\n */ function checkRateLimit(identifier, type) {\n    const config = RATE_LIMITS[type];\n    const now = Date.now();\n    const key = `${type}:${identifier}`;\n    const record = rateLimitStore.get(key);\n    // No previous record or window expired\n    if (!record || now > record.resetTime) {\n        const newRecord = {\n            count: 1,\n            resetTime: now + config.windowMs\n        };\n        rateLimitStore.set(key, newRecord);\n        return {\n            allowed: true,\n            remaining: config.maxAttempts - 1,\n            resetTime: newRecord.resetTime\n        };\n    }\n    // Check if limit exceeded\n    if (record.count >= config.maxAttempts) {\n        return {\n            allowed: false,\n            remaining: 0,\n            resetTime: record.resetTime\n        };\n    }\n    // Increment count\n    record.count++;\n    rateLimitStore.set(key, record);\n    return {\n        allowed: true,\n        remaining: config.maxAttempts - record.count,\n        resetTime: record.resetTime\n    };\n}\n/**\n * Reset rate limit for a specific identifier and type\n */ function resetRateLimit(identifier, type) {\n    const key = `${type}:${identifier}`;\n    rateLimitStore.delete(key);\n}\n/**\n * Clean up expired rate limit records\n */ function cleanupRateLimits() {\n    const now = Date.now();\n    for (const [key, record] of rateLimitStore.entries()){\n        if (now > record.resetTime) {\n            rateLimitStore.delete(key);\n        }\n    }\n}\n/**\n * Get client IP address from request\n */ function getClientIP(request) {\n    const forwarded = request.headers.get('x-forwarded-for');\n    const realIP = request.headers.get('x-real-ip');\n    const remoteAddr = request.headers.get('remote-addr');\n    if (forwarded) {\n        return forwarded.split(',')[0].trim();\n    }\n    return realIP || remoteAddr || 'unknown';\n}\n/**\n * Input sanitization\n */ function sanitizeInput(input) {\n    if (typeof input !== 'string') return '';\n    return input.trim().replace(/[<>]/g, '') // Remove potential HTML tags\n    .replace(/javascript:/gi, '') // Remove javascript: protocol\n    .replace(/on\\w+=/gi, '') // Remove event handlers\n    .substring(0, 1000); // Limit length\n}\n/**\n * Validate email format\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email) && email.length <= 254;\n}\n/**\n * Validate phone number format\n */ function isValidPhone(phone) {\n    const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n    return phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g, ''));\n}\n/**\n * Check for suspicious patterns in text\n */ function containsSuspiciousContent(text) {\n    const suspiciousPatterns = [\n        /script/gi,\n        /javascript/gi,\n        /vbscript/gi,\n        /onload/gi,\n        /onerror/gi,\n        /onclick/gi,\n        /<iframe/gi,\n        /<object/gi,\n        /<embed/gi,\n        /eval\\(/gi,\n        /document\\.cookie/gi,\n        /window\\.location/gi\n    ];\n    return suspiciousPatterns.some((pattern)=>pattern.test(text));\n}\n/**\n * Validate file upload\n */ function validateFileUpload(file, allowedTypes, maxSize) {\n    // Check file size\n    if (file.size > maxSize) {\n        return {\n            valid: false,\n            error: `File size exceeds ${maxSize / 1024 / 1024}MB limit`\n        };\n    }\n    // Check file type\n    if (!allowedTypes.includes(file.type)) {\n        return {\n            valid: false,\n            error: `File type ${file.type} not allowed`\n        };\n    }\n    // Check file name for suspicious content\n    if (containsSuspiciousContent(file.name)) {\n        return {\n            valid: false,\n            error: 'File name contains suspicious content'\n        };\n    }\n    return {\n        valid: true\n    };\n}\n/**\n * Generate secure random string\n */ function generateSecureToken(length = 32) {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n    let result = '';\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n/**\n * Security headers for API responses\n */ function getSecurityHeaders() {\n    return {\n        'X-Content-Type-Options': 'nosniff',\n        'X-Frame-Options': 'DENY',\n        'X-XSS-Protection': '1; mode=block',\n        'Referrer-Policy': 'strict-origin-when-cross-origin',\n        'Content-Security-Policy': \"default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';\"\n    };\n}\n/**\n * Log security events\n */ function logSecurityEvent(event, details, request) {\n    const timestamp = new Date().toISOString();\n    const clientIP = getClientIP(request);\n    const userAgent = request.headers.get('user-agent') || 'unknown';\n    console.warn(`[SECURITY] ${timestamp} - ${event}`, {\n        ip: clientIP,\n        userAgent,\n        details,\n        url: request.url\n    });\n}\n/**\n * Check if request is from a suspicious source\n */ function isSuspiciousRequest(request) {\n    const userAgent = request.headers.get('user-agent') || '';\n    const referer = request.headers.get('referer') || '';\n    // Check for common bot patterns\n    const botPatterns = [\n        /bot/i,\n        /crawler/i,\n        /spider/i,\n        /scraper/i,\n        /curl/i,\n        /wget/i\n    ];\n    // Check for suspicious user agents\n    if (botPatterns.some((pattern)=>pattern.test(userAgent))) {\n        return true;\n    }\n    // Check for empty user agent\n    if (!userAgent.trim()) {\n        return true;\n    }\n    // Check for suspicious referers\n    if (referer && !referer.includes(request.headers.get('host') || '')) {\n        // External referer - might be suspicious depending on context\n        return false; // Allow for now, but log\n    }\n    return false;\n}\n// Cleanup expired rate limits every 5 minutes\nsetInterval(cleanupRateLimits, 5 * 60 * 1000);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/security.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/jws","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/resend","vendor-chunks/bcryptjs","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freviews%2Froute&page=%2Fapi%2Freviews%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freviews%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();