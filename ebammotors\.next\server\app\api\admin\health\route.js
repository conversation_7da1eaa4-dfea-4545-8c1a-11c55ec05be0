/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/health/route";
exports.ids = ["app/api/admin/health/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fhealth%2Froute&page=%2Fapi%2Fadmin%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fhealth%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fhealth%2Froute&page=%2Fapi%2Fadmin%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fhealth%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_website_ebammotors_src_app_api_admin_health_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/health/route.ts */ \"(rsc)/./src/app/api/admin/health/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/health/route\",\n        pathname: \"/api/admin/health\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/health/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\app\\\\api\\\\admin\\\\health\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_website_ebammotors_src_app_api_admin_health_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fhealth%2Froute&page=%2Fapi%2Fadmin%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fhealth%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/health/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/admin/health/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_adminMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/adminMiddleware */ \"(rsc)/./src/lib/adminMiddleware.ts\");\n/* harmony import */ var _lib_monitoring__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/monitoring */ \"(rsc)/./src/lib/monitoring.ts\");\n\n\n\nasync function GET(request) {\n    try {\n        // Verify admin authentication\n        const adminAuth = (0,_lib_adminMiddleware__WEBPACK_IMPORTED_MODULE_1__.getAdminAuth)(request);\n        if (!adminAuth.isValid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Get query parameters\n        const { searchParams } = new URL(request.url);\n        const detailed = searchParams.get('detailed') === 'true';\n        const timeRange = parseInt(searchParams.get('timeRange') || '3600000'); // Default 1 hour\n        // Get comprehensive health information\n        const [healthStatus, performanceStats, recentErrors, dbHealth, memoryUsage] = await Promise.all([\n            (0,_lib_monitoring__WEBPACK_IMPORTED_MODULE_2__.getHealthStatus)(),\n            (0,_lib_monitoring__WEBPACK_IMPORTED_MODULE_2__.getPerformanceStats)(timeRange),\n            (0,_lib_monitoring__WEBPACK_IMPORTED_MODULE_2__.getRecentErrors)(20),\n            (0,_lib_monitoring__WEBPACK_IMPORTED_MODULE_2__.checkDatabaseHealth)(),\n            (0,_lib_monitoring__WEBPACK_IMPORTED_MODULE_2__.getMemoryUsage)()\n        ]);\n        // Format the response to match the expected structure\n        const healthData = {\n            status: healthStatus.status,\n            timestamp: healthStatus.timestamp,\n            uptime: healthStatus.uptime,\n            issues: healthStatus.issues,\n            stats: healthStatus.stats,\n            database: dbHealth,\n            memory: memoryUsage,\n            recentErrors: recentErrors,\n            performance: performanceStats,\n            system: {\n                nodeVersion: process.version,\n                platform: process.platform,\n                uptime: process.uptime(),\n                pid: process.pid\n            }\n        };\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(healthData);\n        // Add security headers\n        const { getSecurityHeaders } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_security_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/security */ \"(rsc)/./src/lib/security.ts\"));\n        const securityHeaders = getSecurityHeaders();\n        Object.entries(securityHeaders).forEach(([key, value])=>{\n            response.headers.set(key, value);\n        });\n        return response;\n    } catch (error) {\n        console.error('Health check error:', error);\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Health check failed',\n            error: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n        // Add security headers even for errors\n        const { getSecurityHeaders } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_security_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/security */ \"(rsc)/./src/lib/security.ts\"));\n        const securityHeaders = getSecurityHeaders();\n        Object.entries(securityHeaders).forEach(([key, value])=>{\n            response.headers.set(key, value);\n        });\n        return response;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/health/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/adminMiddleware.ts":
/*!************************************!*\
  !*** ./src/lib/adminMiddleware.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAdminAuth: () => (/* binding */ getAdminAuth),\n/* harmony export */   getAdminFromRequest: () => (/* binding */ getAdminFromRequest),\n/* harmony export */   verifyLegacyAdminKey: () => (/* binding */ verifyLegacyAdminKey),\n/* harmony export */   withAdminAuth: () => (/* binding */ withAdminAuth)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\n/**\n * Middleware to verify admin authentication for API routes\n */ function withAdminAuth(handler) {\n    return async (request, context)=>{\n        try {\n            // Get authentication from headers or cookies\n            const authHeader = request.headers.get('authorization');\n            const sessionId = request.cookies.get('admin_session')?.value;\n            // Verify authentication\n            const authResult = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyAdminAuth)(authHeader, sessionId);\n            if (!authResult.isValid) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: authResult.message\n                }, {\n                    status: 401\n                });\n            }\n            // Add admin info to request headers for the handler\n            const requestWithAuth = new next_server__WEBPACK_IMPORTED_MODULE_0__.NextRequest(request.url, {\n                method: request.method,\n                headers: {\n                    ...Object.fromEntries(request.headers.entries()),\n                    'x-admin-id': authResult.adminId || 'admin',\n                    'x-admin-authenticated': 'true'\n                },\n                body: request.body\n            });\n            return handler(requestWithAuth, context);\n        } catch (error) {\n            console.error('Admin middleware error:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Authentication error'\n            }, {\n                status: 500\n            });\n        }\n    };\n}\n/**\n * Extract admin authentication from request\n */ function getAdminFromRequest(request) {\n    const adminId = request.headers.get('x-admin-id') || 'admin';\n    const isAuthenticated = request.headers.get('x-admin-authenticated') === 'true';\n    return {\n        adminId,\n        isAuthenticated\n    };\n}\n/**\n * Verify admin authentication for legacy API routes that use adminKey\n */ function verifyLegacyAdminKey(adminKey) {\n    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';\n    return adminKey === validAdminKey;\n}\n/**\n * Get admin authentication from request (supports both new and legacy methods)\n */ function getAdminAuth(request, body) {\n    // Try new authentication method first\n    const authHeader = request.headers.get('authorization');\n    const sessionId = request.cookies.get('admin_session')?.value;\n    const authResult = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyAdminAuth)(authHeader, sessionId);\n    if (authResult.isValid) {\n        return {\n            isValid: true,\n            adminId: authResult.adminId,\n            method: 'token/session'\n        };\n    }\n    // Fall back to legacy adminKey method\n    const adminKey = body?.adminKey || request.nextUrl.searchParams.get('adminKey');\n    if (adminKey && verifyLegacyAdminKey(adminKey)) {\n        return {\n            isValid: true,\n            adminId: 'admin',\n            method: 'legacy'\n        };\n    }\n    return {\n        isValid: false,\n        method: 'none'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/adminMiddleware.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateAdmin: () => (/* binding */ authenticateAdmin),\n/* harmony export */   checkAuthRateLimit: () => (/* binding */ checkAuthRateLimit),\n/* harmony export */   createAdminSession: () => (/* binding */ createAdminSession),\n/* harmony export */   destroyAdminSession: () => (/* binding */ destroyAdminSession),\n/* harmony export */   generateAdminToken: () => (/* binding */ generateAdminToken),\n/* harmony export */   getAdminPasswordHash: () => (/* binding */ getAdminPasswordHash),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   resetAuthRateLimit: () => (/* binding */ resetAuthRateLimit),\n/* harmony export */   validateAdminSession: () => (/* binding */ validateAdminSession),\n/* harmony export */   verifyAdminAuth: () => (/* binding */ verifyAdminAuth),\n/* harmony export */   verifyAdminToken: () => (/* binding */ verifyAdminToken),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Security configuration\nconst SALT_ROUNDS = 12;\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';\nconst JWT_EXPIRES_IN = '24h';\nconst SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n// In-memory session store (replace with Redis in production)\nconst activeSessions = new Map();\n/**\n * Hash a password using bcrypt\n */ async function hashPassword(password) {\n    try {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, SALT_ROUNDS);\n    } catch (error) {\n        console.error('Error hashing password:', error);\n        throw new Error('Failed to hash password');\n    }\n}\n/**\n * Verify a password against its hash\n */ async function verifyPassword(password, hash) {\n    try {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hash);\n    } catch (error) {\n        console.error('Error verifying password:', error);\n        return false;\n    }\n}\n/**\n * Generate a JWT token for admin authentication\n */ function generateAdminToken(adminId = 'admin') {\n    try {\n        const payload = {\n            id: adminId,\n            isAdmin: true,\n            iat: Math.floor(Date.now() / 1000)\n        };\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN\n        });\n    } catch (error) {\n        console.error('Error generating token:', error);\n        throw new Error('Failed to generate authentication token');\n    }\n}\n/**\n * Verify and decode a JWT token\n */ function verifyAdminToken(token) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        if (decoded.isAdmin) {\n            return {\n                id: decoded.id,\n                isAdmin: decoded.isAdmin\n            };\n        }\n        return null;\n    } catch (error) {\n        // Token is invalid or expired\n        return null;\n    }\n}\n/**\n * Create a new admin session\n */ function createAdminSession(adminId = 'admin') {\n    const sessionId = generateSessionId();\n    const now = Date.now();\n    const session = {\n        id: adminId,\n        isAdmin: true,\n        createdAt: now,\n        expiresAt: now + SESSION_TIMEOUT,\n        lastActivity: now\n    };\n    activeSessions.set(sessionId, session);\n    // Clean up expired sessions\n    cleanupExpiredSessions();\n    return sessionId;\n}\n/**\n * Validate an admin session\n */ function validateAdminSession(sessionId) {\n    const session = activeSessions.get(sessionId);\n    if (!session) {\n        return null;\n    }\n    const now = Date.now();\n    // Check if session has expired\n    if (now > session.expiresAt) {\n        activeSessions.delete(sessionId);\n        return null;\n    }\n    // Update last activity\n    session.lastActivity = now;\n    activeSessions.set(sessionId, session);\n    return session;\n}\n/**\n * Destroy an admin session\n */ function destroyAdminSession(sessionId) {\n    return activeSessions.delete(sessionId);\n}\n/**\n * Generate a secure session ID\n */ function generateSessionId() {\n    return `admin_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;\n}\n/**\n * Clean up expired sessions\n */ function cleanupExpiredSessions() {\n    const now = Date.now();\n    for (const [sessionId, session] of activeSessions.entries()){\n        if (now > session.expiresAt) {\n            activeSessions.delete(sessionId);\n        }\n    }\n}\n/**\n * Get admin password hash from environment\n * In production, this should be stored in a secure database\n */ function getAdminPasswordHash() {\n    // For backward compatibility, check if password is already hashed\n    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';\n    // If it starts with $2a$, $2b$, or $2y$, it's already a bcrypt hash\n    if (adminPassword.startsWith('$2a$') || adminPassword.startsWith('$2b$') || adminPassword.startsWith('$2y$')) {\n        return adminPassword;\n    }\n    // For development/migration: return the plain password (will be handled in auth route)\n    return adminPassword;\n}\n/**\n * Secure admin authentication\n */ async function authenticateAdmin(password) {\n    try {\n        const adminPasswordHash = getAdminPasswordHash();\n        let isValid = false;\n        // Check if stored password is hashed or plain text\n        if (adminPasswordHash.startsWith('$2a$') || adminPasswordHash.startsWith('$2b$') || adminPasswordHash.startsWith('$2y$')) {\n            // Password is hashed, use bcrypt comparison\n            isValid = await verifyPassword(password, adminPasswordHash);\n        } else {\n            // Password is plain text (development/migration), use direct comparison\n            isValid = password === adminPasswordHash;\n        }\n        if (isValid) {\n            const token = generateAdminToken();\n            const sessionId = createAdminSession();\n            return {\n                success: true,\n                token,\n                sessionId,\n                message: 'Authentication successful'\n            };\n        } else {\n            return {\n                success: false,\n                message: 'Invalid credentials'\n            };\n        }\n    } catch (error) {\n        console.error('Authentication error:', error);\n        return {\n            success: false,\n            message: 'Authentication failed'\n        };\n    }\n}\n/**\n * Middleware to verify admin authentication\n */ function verifyAdminAuth(authHeader, sessionId) {\n    // Check JWT token\n    if (authHeader && authHeader.startsWith('Bearer ')) {\n        const token = authHeader.substring(7);\n        const decoded = verifyAdminToken(token);\n        if (decoded) {\n            return {\n                isValid: true,\n                adminId: decoded.id,\n                message: 'Token authentication successful'\n            };\n        }\n    }\n    // Check session ID\n    if (sessionId) {\n        const session = validateAdminSession(sessionId);\n        if (session) {\n            return {\n                isValid: true,\n                adminId: session.id,\n                message: 'Session authentication successful'\n            };\n        }\n    }\n    return {\n        isValid: false,\n        message: 'Authentication required'\n    };\n}\n/**\n * Rate limiting for authentication attempts\n */ const authAttempts = new Map();\nconst MAX_AUTH_ATTEMPTS = 5;\nconst AUTH_LOCKOUT_TIME = 15 * 60 * 1000; // 15 minutes\nfunction checkAuthRateLimit(ip) {\n    const now = Date.now();\n    const attempts = authAttempts.get(ip);\n    if (!attempts) {\n        authAttempts.set(ip, {\n            count: 1,\n            lastAttempt: now\n        });\n        return {\n            allowed: true,\n            remainingAttempts: MAX_AUTH_ATTEMPTS - 1\n        };\n    }\n    // Reset if lockout time has passed\n    if (now - attempts.lastAttempt > AUTH_LOCKOUT_TIME) {\n        authAttempts.set(ip, {\n            count: 1,\n            lastAttempt: now\n        });\n        return {\n            allowed: true,\n            remainingAttempts: MAX_AUTH_ATTEMPTS - 1\n        };\n    }\n    // Check if max attempts exceeded\n    if (attempts.count >= MAX_AUTH_ATTEMPTS) {\n        const lockoutTime = AUTH_LOCKOUT_TIME - (now - attempts.lastAttempt);\n        return {\n            allowed: false,\n            remainingAttempts: 0,\n            lockoutTime\n        };\n    }\n    // Increment attempt count\n    attempts.count++;\n    attempts.lastAttempt = now;\n    authAttempts.set(ip, attempts);\n    return {\n        allowed: true,\n        remainingAttempts: MAX_AUTH_ATTEMPTS - attempts.count\n    };\n}\nfunction resetAuthRateLimit(ip) {\n    authAttempts.delete(ip);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/monitoring.ts":
/*!*******************************!*\
  !*** ./src/lib/monitoring.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkDatabaseHealth: () => (/* binding */ checkDatabaseHealth),\n/* harmony export */   cleanupOldData: () => (/* binding */ cleanupOldData),\n/* harmony export */   getHealthStatus: () => (/* binding */ getHealthStatus),\n/* harmony export */   getMemoryUsage: () => (/* binding */ getMemoryUsage),\n/* harmony export */   getPerformanceStats: () => (/* binding */ getPerformanceStats),\n/* harmony export */   getRecentErrors: () => (/* binding */ getRecentErrors),\n/* harmony export */   logError: () => (/* binding */ logError),\n/* harmony export */   startPerformanceMonitoring: () => (/* binding */ startPerformanceMonitoring)\n/* harmony export */ });\n// In-memory storage (replace with database/external service in production)\nconst performanceMetrics = [];\nconst errorLogs = [];\nconst MAX_STORED_METRICS = 1000;\nconst MAX_STORED_ERRORS = 500;\n/**\n * Start performance monitoring for a request\n */ function startPerformanceMonitoring(request) {\n    const startTime = Date.now();\n    const endpoint = new URL(request.url).pathname;\n    const method = request.method;\n    return {\n        endpoint,\n        method,\n        startTime,\n        finish: (status, error)=>{\n            const duration = Date.now() - startTime;\n            const userAgent = request.headers.get('user-agent') || undefined;\n            const ip = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined;\n            const metric = {\n                timestamp: Date.now(),\n                endpoint,\n                method,\n                duration,\n                status,\n                userAgent,\n                ip,\n                error\n            };\n            // Store metric\n            performanceMetrics.push(metric);\n            // Keep only recent metrics\n            if (performanceMetrics.length > MAX_STORED_METRICS) {\n                performanceMetrics.splice(0, performanceMetrics.length - MAX_STORED_METRICS);\n            }\n            // Log slow requests\n            if (duration > 5000) {\n                console.warn(`[PERFORMANCE] Slow request detected: ${method} ${endpoint} took ${duration}ms`);\n            }\n            // Log errors\n            if (status >= 400) {\n                console.error(`[ERROR] ${method} ${endpoint} returned ${status}${error ? `: ${error}` : ''}`);\n            }\n        }\n    };\n}\n/**\n * Log an error with context\n */ function logError(error, context) {\n    const errorMessage = error instanceof Error ? error.message : error;\n    const stack = error instanceof Error ? error.stack : undefined;\n    const errorLog = {\n        timestamp: Date.now(),\n        error: errorMessage,\n        stack,\n        endpoint: context.endpoint || 'unknown',\n        method: context.method || 'unknown',\n        userAgent: context.request?.headers.get('user-agent') || undefined,\n        ip: context.request?.headers.get('x-forwarded-for') || context.request?.headers.get('x-real-ip') || undefined,\n        context: context.additionalContext\n    };\n    // Store error\n    errorLogs.push(errorLog);\n    // Keep only recent errors\n    if (errorLogs.length > MAX_STORED_ERRORS) {\n        errorLogs.splice(0, errorLogs.length - MAX_STORED_ERRORS);\n    }\n    // Console log for immediate visibility\n    console.error(`[ERROR] ${errorMessage}`, {\n        endpoint: errorLog.endpoint,\n        method: errorLog.method,\n        ip: errorLog.ip,\n        context: errorLog.context\n    });\n}\n/**\n * Get performance statistics\n */ function getPerformanceStats(timeRange = 60 * 60 * 1000) {\n    const now = Date.now();\n    const cutoff = now - timeRange;\n    const recentMetrics = performanceMetrics.filter((m)=>m.timestamp > cutoff);\n    if (recentMetrics.length === 0) {\n        return {\n            totalRequests: 0,\n            averageResponseTime: 0,\n            errorRate: 0,\n            slowRequests: 0,\n            endpointStats: {}\n        };\n    }\n    const totalRequests = recentMetrics.length;\n    const averageResponseTime = recentMetrics.reduce((sum, m)=>sum + m.duration, 0) / totalRequests;\n    const errorCount = recentMetrics.filter((m)=>m.status >= 400).length;\n    const errorRate = errorCount / totalRequests * 100;\n    const slowRequests = recentMetrics.filter((m)=>m.duration > 5000).length;\n    // Group by endpoint\n    const endpointStats = {};\n    recentMetrics.forEach((metric)=>{\n        if (!endpointStats[metric.endpoint]) {\n            endpointStats[metric.endpoint] = {\n                requests: 0,\n                averageTime: 0,\n                errors: 0,\n                slowRequests: 0\n            };\n        }\n        const stats = endpointStats[metric.endpoint];\n        stats.requests++;\n        stats.averageTime = (stats.averageTime * (stats.requests - 1) + metric.duration) / stats.requests;\n        if (metric.status >= 400) stats.errors++;\n        if (metric.duration > 5000) stats.slowRequests++;\n    });\n    return {\n        totalRequests,\n        averageResponseTime: Math.round(averageResponseTime),\n        errorRate: Math.round(errorRate * 100) / 100,\n        slowRequests,\n        endpointStats\n    };\n}\n/**\n * Get recent errors\n */ function getRecentErrors(limit = 50) {\n    return errorLogs.slice(-limit).reverse() // Most recent first\n    .map((error)=>({\n            timestamp: new Date(error.timestamp).toISOString(),\n            error: error.error,\n            endpoint: error.endpoint,\n            method: error.method,\n            ip: error.ip,\n            context: error.context\n        }));\n}\n/**\n * Health check function\n */ function getHealthStatus() {\n    const stats = getPerformanceStats(5 * 60 * 1000); // Last 5 minutes\n    const recentErrors = getRecentErrors(10);\n    // Determine health status\n    let status = 'healthy';\n    const issues = [];\n    if (stats.errorRate > 10) {\n        status = 'unhealthy';\n        issues.push(`High error rate: ${stats.errorRate}%`);\n    } else if (stats.errorRate > 5) {\n        status = 'degraded';\n        issues.push(`Elevated error rate: ${stats.errorRate}%`);\n    }\n    if (stats.averageResponseTime > 10000) {\n        status = 'unhealthy';\n        issues.push(`Very slow response time: ${stats.averageResponseTime}ms`);\n    } else if (stats.averageResponseTime > 5000) {\n        if (status === 'healthy') status = 'degraded';\n        issues.push(`Slow response time: ${stats.averageResponseTime}ms`);\n    }\n    if (stats.slowRequests > stats.totalRequests * 0.1) {\n        if (status === 'healthy') status = 'degraded';\n        issues.push(`High number of slow requests: ${stats.slowRequests}`);\n    }\n    return {\n        status,\n        timestamp: new Date().toISOString(),\n        uptime: process.uptime(),\n        issues,\n        stats: {\n            requests: stats.totalRequests,\n            averageResponseTime: stats.averageResponseTime,\n            errorRate: stats.errorRate,\n            slowRequests: stats.slowRequests\n        },\n        recentErrors: recentErrors.slice(0, 3) // Only show 3 most recent errors\n    };\n}\n/**\n * Database health check\n */ async function checkDatabaseHealth() {\n    try {\n        const start = Date.now();\n        // Simple database query to check connectivity\n        // This should be replaced with actual database health check\n        const { sql } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@neondatabase\"), __webpack_require__.e(\"vendor-chunks/ws\"), __webpack_require__.e(\"vendor-chunks/@vercel\"), __webpack_require__.e(\"vendor-chunks/node-gyp-build\"), __webpack_require__.e(\"vendor-chunks/bufferutil\"), __webpack_require__.e(\"_66e9\")]).then(__webpack_require__.bind(__webpack_require__, /*! @vercel/postgres */ \"(rsc)/./node_modules/@vercel/postgres/dist/index-node.js\"));\n        await sql`SELECT 1 as health_check`;\n        const latency = Date.now() - start;\n        return {\n            healthy: true,\n            latency\n        };\n    } catch (error) {\n        return {\n            healthy: false,\n            error: error instanceof Error ? error.message : 'Unknown database error'\n        };\n    }\n}\n/**\n * Memory usage monitoring\n */ function getMemoryUsage() {\n    const usage = process.memoryUsage();\n    return {\n        rss: Math.round(usage.rss / 1024 / 1024),\n        heapTotal: Math.round(usage.heapTotal / 1024 / 1024),\n        heapUsed: Math.round(usage.heapUsed / 1024 / 1024),\n        external: Math.round(usage.external / 1024 / 1024),\n        arrayBuffers: Math.round(usage.arrayBuffers / 1024 / 1024)\n    };\n}\n/**\n * Clean up old metrics and errors\n */ function cleanupOldData() {\n    const oneHourAgo = Date.now() - 60 * 60 * 1000;\n    // Remove old performance metrics\n    const validMetrics = performanceMetrics.filter((m)=>m.timestamp > oneHourAgo);\n    performanceMetrics.splice(0, performanceMetrics.length, ...validMetrics);\n    // Remove old error logs\n    const validErrors = errorLogs.filter((e)=>e.timestamp > oneHourAgo);\n    errorLogs.splice(0, errorLogs.length, ...validErrors);\n}\n// Clean up old data every 10 minutes\nsetInterval(cleanupOldData, 10 * 60 * 1000);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL21vbml0b3JpbmcudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUF5QkEsMkVBQTJFO0FBQzNFLE1BQU1BLHFCQUEwQyxFQUFFO0FBQ2xELE1BQU1DLFlBQXdCLEVBQUU7QUFDaEMsTUFBTUMscUJBQXFCO0FBQzNCLE1BQU1DLG9CQUFvQjtBQUUxQjs7Q0FFQyxHQUNNLFNBQVNDLDJCQUEyQkMsT0FBb0I7SUFDN0QsTUFBTUMsWUFBWUMsS0FBS0MsR0FBRztJQUMxQixNQUFNQyxXQUFXLElBQUlDLElBQUlMLFFBQVFNLEdBQUcsRUFBRUMsUUFBUTtJQUM5QyxNQUFNQyxTQUFTUixRQUFRUSxNQUFNO0lBRTdCLE9BQU87UUFDTEo7UUFDQUk7UUFDQVA7UUFDQVEsUUFBUSxDQUFDQyxRQUFnQkM7WUFDdkIsTUFBTUMsV0FBV1YsS0FBS0MsR0FBRyxLQUFLRjtZQUM5QixNQUFNWSxZQUFZYixRQUFRYyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxpQkFBaUJDO1lBQ3ZELE1BQU1DLEtBQUtqQixRQUFRYyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxzQkFDckJmLFFBQVFjLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGdCQUFnQkM7WUFFOUMsTUFBTUUsU0FBNEI7Z0JBQ2hDQyxXQUFXakIsS0FBS0MsR0FBRztnQkFDbkJDO2dCQUNBSTtnQkFDQUk7Z0JBQ0FGO2dCQUNBRztnQkFDQUk7Z0JBQ0FOO1lBQ0Y7WUFFQSxlQUFlO1lBQ2ZoQixtQkFBbUJ5QixJQUFJLENBQUNGO1lBRXhCLDJCQUEyQjtZQUMzQixJQUFJdkIsbUJBQW1CMEIsTUFBTSxHQUFHeEIsb0JBQW9CO2dCQUNsREYsbUJBQW1CMkIsTUFBTSxDQUFDLEdBQUczQixtQkFBbUIwQixNQUFNLEdBQUd4QjtZQUMzRDtZQUVBLG9CQUFvQjtZQUNwQixJQUFJZSxXQUFXLE1BQU07Z0JBQ25CVyxRQUFRQyxJQUFJLENBQUMsQ0FBQyxxQ0FBcUMsRUFBRWhCLE9BQU8sQ0FBQyxFQUFFSixTQUFTLE1BQU0sRUFBRVEsU0FBUyxFQUFFLENBQUM7WUFDOUY7WUFFQSxhQUFhO1lBQ2IsSUFBSUYsVUFBVSxLQUFLO2dCQUNqQmEsUUFBUVosS0FBSyxDQUFDLENBQUMsUUFBUSxFQUFFSCxPQUFPLENBQUMsRUFBRUosU0FBUyxVQUFVLEVBQUVNLFNBQVNDLFFBQVEsQ0FBQyxFQUFFLEVBQUVBLE9BQU8sR0FBRyxJQUFJO1lBQzlGO1FBQ0Y7SUFDRjtBQUNGO0FBRUE7O0NBRUMsR0FDTSxTQUFTYyxTQUNkZCxLQUFxQixFQUNyQmUsT0FLQztJQUVELE1BQU1DLGVBQWVoQixpQkFBaUJpQixRQUFRakIsTUFBTWtCLE9BQU8sR0FBR2xCO0lBQzlELE1BQU1tQixRQUFRbkIsaUJBQWlCaUIsUUFBUWpCLE1BQU1tQixLQUFLLEdBQUdkO0lBRXJELE1BQU1lLFdBQXFCO1FBQ3pCWixXQUFXakIsS0FBS0MsR0FBRztRQUNuQlEsT0FBT2dCO1FBQ1BHO1FBQ0ExQixVQUFVc0IsUUFBUXRCLFFBQVEsSUFBSTtRQUM5QkksUUFBUWtCLFFBQVFsQixNQUFNLElBQUk7UUFDMUJLLFdBQVdhLFFBQVExQixPQUFPLEVBQUVjLFFBQVFDLElBQUksaUJBQWlCQztRQUN6REMsSUFBSVMsUUFBUTFCLE9BQU8sRUFBRWMsUUFBUUMsSUFBSSxzQkFDN0JXLFFBQVExQixPQUFPLEVBQUVjLFFBQVFDLElBQUksZ0JBQWdCQztRQUNqRFUsU0FBU0EsUUFBUU0saUJBQWlCO0lBQ3BDO0lBRUEsY0FBYztJQUNkcEMsVUFBVXdCLElBQUksQ0FBQ1c7SUFFZiwwQkFBMEI7SUFDMUIsSUFBSW5DLFVBQVV5QixNQUFNLEdBQUd2QixtQkFBbUI7UUFDeENGLFVBQVUwQixNQUFNLENBQUMsR0FBRzFCLFVBQVV5QixNQUFNLEdBQUd2QjtJQUN6QztJQUVBLHVDQUF1QztJQUN2Q3lCLFFBQVFaLEtBQUssQ0FBQyxDQUFDLFFBQVEsRUFBRWdCLGNBQWMsRUFBRTtRQUN2Q3ZCLFVBQVUyQixTQUFTM0IsUUFBUTtRQUMzQkksUUFBUXVCLFNBQVN2QixNQUFNO1FBQ3ZCUyxJQUFJYyxTQUFTZCxFQUFFO1FBQ2ZTLFNBQVNLLFNBQVNMLE9BQU87SUFDM0I7QUFDRjtBQUVBOztDQUVDLEdBQ00sU0FBU08sb0JBQW9CQyxZQUFvQixLQUFLLEtBQUssSUFBSTtJQUNwRSxNQUFNL0IsTUFBTUQsS0FBS0MsR0FBRztJQUNwQixNQUFNZ0MsU0FBU2hDLE1BQU0rQjtJQUVyQixNQUFNRSxnQkFBZ0J6QyxtQkFBbUIwQyxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVuQixTQUFTLEdBQUdnQjtJQUVuRSxJQUFJQyxjQUFjZixNQUFNLEtBQUssR0FBRztRQUM5QixPQUFPO1lBQ0xrQixlQUFlO1lBQ2ZDLHFCQUFxQjtZQUNyQkMsV0FBVztZQUNYQyxjQUFjO1lBQ2RDLGVBQWUsQ0FBQztRQUNsQjtJQUNGO0lBRUEsTUFBTUosZ0JBQWdCSCxjQUFjZixNQUFNO0lBQzFDLE1BQU1tQixzQkFBc0JKLGNBQWNRLE1BQU0sQ0FBQyxDQUFDQyxLQUFLUCxJQUFNTyxNQUFNUCxFQUFFMUIsUUFBUSxFQUFFLEtBQUsyQjtJQUNwRixNQUFNTyxhQUFhVixjQUFjQyxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUU1QixNQUFNLElBQUksS0FBS1csTUFBTTtJQUNwRSxNQUFNb0IsWUFBWSxhQUFjRixnQkFBaUI7SUFDakQsTUFBTUcsZUFBZU4sY0FBY0MsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFMUIsUUFBUSxHQUFHLE1BQU1TLE1BQU07SUFFeEUsb0JBQW9CO0lBQ3BCLE1BQU1zQixnQkFLRCxDQUFDO0lBRU5QLGNBQWNXLE9BQU8sQ0FBQzdCLENBQUFBO1FBQ3BCLElBQUksQ0FBQ3lCLGFBQWEsQ0FBQ3pCLE9BQU9kLFFBQVEsQ0FBQyxFQUFFO1lBQ25DdUMsYUFBYSxDQUFDekIsT0FBT2QsUUFBUSxDQUFDLEdBQUc7Z0JBQy9CNEMsVUFBVTtnQkFDVkMsYUFBYTtnQkFDYkMsUUFBUTtnQkFDUlIsY0FBYztZQUNoQjtRQUNGO1FBRUEsTUFBTVMsUUFBUVIsYUFBYSxDQUFDekIsT0FBT2QsUUFBUSxDQUFDO1FBQzVDK0MsTUFBTUgsUUFBUTtRQUNkRyxNQUFNRixXQUFXLEdBQUcsQ0FBQ0UsTUFBTUYsV0FBVyxHQUFJRSxDQUFBQSxNQUFNSCxRQUFRLEdBQUcsS0FBSzlCLE9BQU9OLFFBQVEsSUFBSXVDLE1BQU1ILFFBQVE7UUFFakcsSUFBSTlCLE9BQU9SLE1BQU0sSUFBSSxLQUFLeUMsTUFBTUQsTUFBTTtRQUN0QyxJQUFJaEMsT0FBT04sUUFBUSxHQUFHLE1BQU11QyxNQUFNVCxZQUFZO0lBQ2hEO0lBRUEsT0FBTztRQUNMSDtRQUNBQyxxQkFBcUJZLEtBQUtDLEtBQUssQ0FBQ2I7UUFDaENDLFdBQVdXLEtBQUtDLEtBQUssQ0FBQ1osWUFBWSxPQUFPO1FBQ3pDQztRQUNBQztJQUNGO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLFNBQVNXLGdCQUFnQkMsUUFBZ0IsRUFBRTtJQUNoRCxPQUFPM0QsVUFDSjRELEtBQUssQ0FBQyxDQUFDRCxPQUNQRSxPQUFPLEdBQUcsb0JBQW9CO0tBQzlCQyxHQUFHLENBQUMvQyxDQUFBQSxRQUFVO1lBQ2JRLFdBQVcsSUFBSWpCLEtBQUtTLE1BQU1RLFNBQVMsRUFBRXdDLFdBQVc7WUFDaERoRCxPQUFPQSxNQUFNQSxLQUFLO1lBQ2xCUCxVQUFVTyxNQUFNUCxRQUFRO1lBQ3hCSSxRQUFRRyxNQUFNSCxNQUFNO1lBQ3BCUyxJQUFJTixNQUFNTSxFQUFFO1lBQ1pTLFNBQVNmLE1BQU1lLE9BQU87UUFDeEI7QUFDSjtBQUVBOztDQUVDLEdBQ00sU0FBU2tDO0lBQ2QsTUFBTVQsUUFBUWxCLG9CQUFvQixJQUFJLEtBQUssT0FBTyxpQkFBaUI7SUFDbkUsTUFBTTRCLGVBQWVQLGdCQUFnQjtJQUVyQywwQkFBMEI7SUFDMUIsSUFBSTVDLFNBQVM7SUFDYixNQUFNb0QsU0FBbUIsRUFBRTtJQUUzQixJQUFJWCxNQUFNVixTQUFTLEdBQUcsSUFBSTtRQUN4Qi9CLFNBQVM7UUFDVG9ELE9BQU8xQyxJQUFJLENBQUMsQ0FBQyxpQkFBaUIsRUFBRStCLE1BQU1WLFNBQVMsQ0FBQyxDQUFDLENBQUM7SUFDcEQsT0FBTyxJQUFJVSxNQUFNVixTQUFTLEdBQUcsR0FBRztRQUM5Qi9CLFNBQVM7UUFDVG9ELE9BQU8xQyxJQUFJLENBQUMsQ0FBQyxxQkFBcUIsRUFBRStCLE1BQU1WLFNBQVMsQ0FBQyxDQUFDLENBQUM7SUFDeEQ7SUFFQSxJQUFJVSxNQUFNWCxtQkFBbUIsR0FBRyxPQUFPO1FBQ3JDOUIsU0FBUztRQUNUb0QsT0FBTzFDLElBQUksQ0FBQyxDQUFDLHlCQUF5QixFQUFFK0IsTUFBTVgsbUJBQW1CLENBQUMsRUFBRSxDQUFDO0lBQ3ZFLE9BQU8sSUFBSVcsTUFBTVgsbUJBQW1CLEdBQUcsTUFBTTtRQUMzQyxJQUFJOUIsV0FBVyxXQUFXQSxTQUFTO1FBQ25Db0QsT0FBTzFDLElBQUksQ0FBQyxDQUFDLG9CQUFvQixFQUFFK0IsTUFBTVgsbUJBQW1CLENBQUMsRUFBRSxDQUFDO0lBQ2xFO0lBRUEsSUFBSVcsTUFBTVQsWUFBWSxHQUFHUyxNQUFNWixhQUFhLEdBQUcsS0FBSztRQUNsRCxJQUFJN0IsV0FBVyxXQUFXQSxTQUFTO1FBQ25Db0QsT0FBTzFDLElBQUksQ0FBQyxDQUFDLDhCQUE4QixFQUFFK0IsTUFBTVQsWUFBWSxFQUFFO0lBQ25FO0lBRUEsT0FBTztRQUNMaEM7UUFDQVMsV0FBVyxJQUFJakIsT0FBT3lELFdBQVc7UUFDakNJLFFBQVFDLFFBQVFELE1BQU07UUFDdEJEO1FBQ0FYLE9BQU87WUFDTEgsVUFBVUcsTUFBTVosYUFBYTtZQUM3QkMscUJBQXFCVyxNQUFNWCxtQkFBbUI7WUFDOUNDLFdBQVdVLE1BQU1WLFNBQVM7WUFDMUJDLGNBQWNTLE1BQU1ULFlBQVk7UUFDbEM7UUFDQW1CLGNBQWNBLGFBQWFMLEtBQUssQ0FBQyxHQUFHLEdBQUcsaUNBQWlDO0lBQzFFO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLGVBQWVTO0lBQ3BCLElBQUk7UUFDRixNQUFNQyxRQUFRaEUsS0FBS0MsR0FBRztRQUV0Qiw4Q0FBOEM7UUFDOUMsNERBQTREO1FBQzVELE1BQU0sRUFBRWdFLEdBQUcsRUFBRSxHQUFHLE1BQU0sK2JBQTBCO1FBQ2hELE1BQU1BLEdBQUcsQ0FBQyx3QkFBd0IsQ0FBQztRQUVuQyxNQUFNQyxVQUFVbEUsS0FBS0MsR0FBRyxLQUFLK0Q7UUFFN0IsT0FBTztZQUFFRyxTQUFTO1lBQU1EO1FBQVE7SUFDbEMsRUFBRSxPQUFPekQsT0FBTztRQUNkLE9BQU87WUFDTDBELFNBQVM7WUFDVDFELE9BQU9BLGlCQUFpQmlCLFFBQVFqQixNQUFNa0IsT0FBTyxHQUFHO1FBQ2xEO0lBQ0Y7QUFDRjtBQUVBOztDQUVDLEdBQ00sU0FBU3lDO0lBQ2QsTUFBTUMsUUFBUVAsUUFBUVEsV0FBVztJQUVqQyxPQUFPO1FBQ0xDLEtBQUtyQixLQUFLQyxLQUFLLENBQUNrQixNQUFNRSxHQUFHLEdBQUcsT0FBTztRQUNuQ0MsV0FBV3RCLEtBQUtDLEtBQUssQ0FBQ2tCLE1BQU1HLFNBQVMsR0FBRyxPQUFPO1FBQy9DQyxVQUFVdkIsS0FBS0MsS0FBSyxDQUFDa0IsTUFBTUksUUFBUSxHQUFHLE9BQU87UUFDN0NDLFVBQVV4QixLQUFLQyxLQUFLLENBQUNrQixNQUFNSyxRQUFRLEdBQUcsT0FBTztRQUM3Q0MsY0FBY3pCLEtBQUtDLEtBQUssQ0FBQ2tCLE1BQU1NLFlBQVksR0FBRyxPQUFPO0lBQ3ZEO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLFNBQVNDO0lBQ2QsTUFBTUMsYUFBYTdFLEtBQUtDLEdBQUcsS0FBTSxLQUFLLEtBQUs7SUFFM0MsaUNBQWlDO0lBQ2pDLE1BQU02RSxlQUFlckYsbUJBQW1CMEMsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFbkIsU0FBUyxHQUFHNEQ7SUFDbEVwRixtQkFBbUIyQixNQUFNLENBQUMsR0FBRzNCLG1CQUFtQjBCLE1BQU0sS0FBSzJEO0lBRTNELHdCQUF3QjtJQUN4QixNQUFNQyxjQUFjckYsVUFBVXlDLE1BQU0sQ0FBQzZDLENBQUFBLElBQUtBLEVBQUUvRCxTQUFTLEdBQUc0RDtJQUN4RG5GLFVBQVUwQixNQUFNLENBQUMsR0FBRzFCLFVBQVV5QixNQUFNLEtBQUs0RDtBQUMzQztBQUVBLHFDQUFxQztBQUNyQ0UsWUFBWUwsZ0JBQWdCLEtBQUssS0FBSyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVc2VyXFxEZXNrdG9wXFx3ZWJzaXRlXFxlYmFtbW90b3JzXFxzcmNcXGxpYlxcbW9uaXRvcmluZy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCB9IGZyb20gJ25leHQvc2VydmVyJztcblxuLy8gUGVyZm9ybWFuY2UgbWV0cmljcyBzdG9yYWdlICh1c2UgZXh0ZXJuYWwgc2VydmljZSBpbiBwcm9kdWN0aW9uKVxuaW50ZXJmYWNlIFBlcmZvcm1hbmNlTWV0cmljIHtcbiAgdGltZXN0YW1wOiBudW1iZXI7XG4gIGVuZHBvaW50OiBzdHJpbmc7XG4gIG1ldGhvZDogc3RyaW5nO1xuICBkdXJhdGlvbjogbnVtYmVyO1xuICBzdGF0dXM6IG51bWJlcjtcbiAgdXNlckFnZW50Pzogc3RyaW5nO1xuICBpcD86IHN0cmluZztcbiAgZXJyb3I/OiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBFcnJvckxvZyB7XG4gIHRpbWVzdGFtcDogbnVtYmVyO1xuICBlcnJvcjogc3RyaW5nO1xuICBzdGFjaz86IHN0cmluZztcbiAgZW5kcG9pbnQ6IHN0cmluZztcbiAgbWV0aG9kOiBzdHJpbmc7XG4gIHVzZXJBZ2VudD86IHN0cmluZztcbiAgaXA/OiBzdHJpbmc7XG4gIGNvbnRleHQ/OiBhbnk7XG59XG5cbi8vIEluLW1lbW9yeSBzdG9yYWdlIChyZXBsYWNlIHdpdGggZGF0YWJhc2UvZXh0ZXJuYWwgc2VydmljZSBpbiBwcm9kdWN0aW9uKVxuY29uc3QgcGVyZm9ybWFuY2VNZXRyaWNzOiBQZXJmb3JtYW5jZU1ldHJpY1tdID0gW107XG5jb25zdCBlcnJvckxvZ3M6IEVycm9yTG9nW10gPSBbXTtcbmNvbnN0IE1BWF9TVE9SRURfTUVUUklDUyA9IDEwMDA7XG5jb25zdCBNQVhfU1RPUkVEX0VSUk9SUyA9IDUwMDtcblxuLyoqXG4gKiBTdGFydCBwZXJmb3JtYW5jZSBtb25pdG9yaW5nIGZvciBhIHJlcXVlc3RcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHN0YXJ0UGVyZm9ybWFuY2VNb25pdG9yaW5nKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIGNvbnN0IHN0YXJ0VGltZSA9IERhdGUubm93KCk7XG4gIGNvbnN0IGVuZHBvaW50ID0gbmV3IFVSTChyZXF1ZXN0LnVybCkucGF0aG5hbWU7XG4gIGNvbnN0IG1ldGhvZCA9IHJlcXVlc3QubWV0aG9kO1xuICBcbiAgcmV0dXJuIHtcbiAgICBlbmRwb2ludCxcbiAgICBtZXRob2QsXG4gICAgc3RhcnRUaW1lLFxuICAgIGZpbmlzaDogKHN0YXR1czogbnVtYmVyLCBlcnJvcj86IHN0cmluZykgPT4ge1xuICAgICAgY29uc3QgZHVyYXRpb24gPSBEYXRlLm5vdygpIC0gc3RhcnRUaW1lO1xuICAgICAgY29uc3QgdXNlckFnZW50ID0gcmVxdWVzdC5oZWFkZXJzLmdldCgndXNlci1hZ2VudCcpIHx8IHVuZGVmaW5lZDtcbiAgICAgIGNvbnN0IGlwID0gcmVxdWVzdC5oZWFkZXJzLmdldCgneC1mb3J3YXJkZWQtZm9yJykgfHwgXG4gICAgICAgICAgICAgICAgcmVxdWVzdC5oZWFkZXJzLmdldCgneC1yZWFsLWlwJykgfHwgdW5kZWZpbmVkO1xuICAgICAgXG4gICAgICBjb25zdCBtZXRyaWM6IFBlcmZvcm1hbmNlTWV0cmljID0ge1xuICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICAgIGVuZHBvaW50LFxuICAgICAgICBtZXRob2QsXG4gICAgICAgIGR1cmF0aW9uLFxuICAgICAgICBzdGF0dXMsXG4gICAgICAgIHVzZXJBZ2VudCxcbiAgICAgICAgaXAsXG4gICAgICAgIGVycm9yXG4gICAgICB9O1xuICAgICAgXG4gICAgICAvLyBTdG9yZSBtZXRyaWNcbiAgICAgIHBlcmZvcm1hbmNlTWV0cmljcy5wdXNoKG1ldHJpYyk7XG4gICAgICBcbiAgICAgIC8vIEtlZXAgb25seSByZWNlbnQgbWV0cmljc1xuICAgICAgaWYgKHBlcmZvcm1hbmNlTWV0cmljcy5sZW5ndGggPiBNQVhfU1RPUkVEX01FVFJJQ1MpIHtcbiAgICAgICAgcGVyZm9ybWFuY2VNZXRyaWNzLnNwbGljZSgwLCBwZXJmb3JtYW5jZU1ldHJpY3MubGVuZ3RoIC0gTUFYX1NUT1JFRF9NRVRSSUNTKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8gTG9nIHNsb3cgcmVxdWVzdHNcbiAgICAgIGlmIChkdXJhdGlvbiA+IDUwMDApIHsgLy8gNSBzZWNvbmRzXG4gICAgICAgIGNvbnNvbGUud2FybihgW1BFUkZPUk1BTkNFXSBTbG93IHJlcXVlc3QgZGV0ZWN0ZWQ6ICR7bWV0aG9kfSAke2VuZHBvaW50fSB0b29rICR7ZHVyYXRpb259bXNgKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8gTG9nIGVycm9yc1xuICAgICAgaWYgKHN0YXR1cyA+PSA0MDApIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihgW0VSUk9SXSAke21ldGhvZH0gJHtlbmRwb2ludH0gcmV0dXJuZWQgJHtzdGF0dXN9JHtlcnJvciA/IGA6ICR7ZXJyb3J9YCA6ICcnfWApO1xuICAgICAgfVxuICAgIH1cbiAgfTtcbn1cblxuLyoqXG4gKiBMb2cgYW4gZXJyb3Igd2l0aCBjb250ZXh0XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBsb2dFcnJvcihcbiAgZXJyb3I6IEVycm9yIHwgc3RyaW5nLCBcbiAgY29udGV4dDoge1xuICAgIGVuZHBvaW50Pzogc3RyaW5nO1xuICAgIG1ldGhvZD86IHN0cmluZztcbiAgICByZXF1ZXN0PzogTmV4dFJlcXVlc3Q7XG4gICAgYWRkaXRpb25hbENvbnRleHQ/OiBhbnk7XG4gIH1cbikge1xuICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IGVycm9yO1xuICBjb25zdCBzdGFjayA9IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5zdGFjayA6IHVuZGVmaW5lZDtcbiAgXG4gIGNvbnN0IGVycm9yTG9nOiBFcnJvckxvZyA9IHtcbiAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgZXJyb3I6IGVycm9yTWVzc2FnZSxcbiAgICBzdGFjayxcbiAgICBlbmRwb2ludDogY29udGV4dC5lbmRwb2ludCB8fCAndW5rbm93bicsXG4gICAgbWV0aG9kOiBjb250ZXh0Lm1ldGhvZCB8fCAndW5rbm93bicsXG4gICAgdXNlckFnZW50OiBjb250ZXh0LnJlcXVlc3Q/LmhlYWRlcnMuZ2V0KCd1c2VyLWFnZW50JykgfHwgdW5kZWZpbmVkLFxuICAgIGlwOiBjb250ZXh0LnJlcXVlc3Q/LmhlYWRlcnMuZ2V0KCd4LWZvcndhcmRlZC1mb3InKSB8fCBcbiAgICAgICAgY29udGV4dC5yZXF1ZXN0Py5oZWFkZXJzLmdldCgneC1yZWFsLWlwJykgfHwgdW5kZWZpbmVkLFxuICAgIGNvbnRleHQ6IGNvbnRleHQuYWRkaXRpb25hbENvbnRleHRcbiAgfTtcbiAgXG4gIC8vIFN0b3JlIGVycm9yXG4gIGVycm9yTG9ncy5wdXNoKGVycm9yTG9nKTtcbiAgXG4gIC8vIEtlZXAgb25seSByZWNlbnQgZXJyb3JzXG4gIGlmIChlcnJvckxvZ3MubGVuZ3RoID4gTUFYX1NUT1JFRF9FUlJPUlMpIHtcbiAgICBlcnJvckxvZ3Muc3BsaWNlKDAsIGVycm9yTG9ncy5sZW5ndGggLSBNQVhfU1RPUkVEX0VSUk9SUyk7XG4gIH1cbiAgXG4gIC8vIENvbnNvbGUgbG9nIGZvciBpbW1lZGlhdGUgdmlzaWJpbGl0eVxuICBjb25zb2xlLmVycm9yKGBbRVJST1JdICR7ZXJyb3JNZXNzYWdlfWAsIHtcbiAgICBlbmRwb2ludDogZXJyb3JMb2cuZW5kcG9pbnQsXG4gICAgbWV0aG9kOiBlcnJvckxvZy5tZXRob2QsXG4gICAgaXA6IGVycm9yTG9nLmlwLFxuICAgIGNvbnRleHQ6IGVycm9yTG9nLmNvbnRleHRcbiAgfSk7XG59XG5cbi8qKlxuICogR2V0IHBlcmZvcm1hbmNlIHN0YXRpc3RpY3NcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldFBlcmZvcm1hbmNlU3RhdHModGltZVJhbmdlOiBudW1iZXIgPSA2MCAqIDYwICogMTAwMCkgeyAvLyBEZWZhdWx0OiAxIGhvdXJcbiAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcbiAgY29uc3QgY3V0b2ZmID0gbm93IC0gdGltZVJhbmdlO1xuICBcbiAgY29uc3QgcmVjZW50TWV0cmljcyA9IHBlcmZvcm1hbmNlTWV0cmljcy5maWx0ZXIobSA9PiBtLnRpbWVzdGFtcCA+IGN1dG9mZik7XG4gIFxuICBpZiAocmVjZW50TWV0cmljcy5sZW5ndGggPT09IDApIHtcbiAgICByZXR1cm4ge1xuICAgICAgdG90YWxSZXF1ZXN0czogMCxcbiAgICAgIGF2ZXJhZ2VSZXNwb25zZVRpbWU6IDAsXG4gICAgICBlcnJvclJhdGU6IDAsXG4gICAgICBzbG93UmVxdWVzdHM6IDAsXG4gICAgICBlbmRwb2ludFN0YXRzOiB7fVxuICAgIH07XG4gIH1cbiAgXG4gIGNvbnN0IHRvdGFsUmVxdWVzdHMgPSByZWNlbnRNZXRyaWNzLmxlbmd0aDtcbiAgY29uc3QgYXZlcmFnZVJlc3BvbnNlVGltZSA9IHJlY2VudE1ldHJpY3MucmVkdWNlKChzdW0sIG0pID0+IHN1bSArIG0uZHVyYXRpb24sIDApIC8gdG90YWxSZXF1ZXN0cztcbiAgY29uc3QgZXJyb3JDb3VudCA9IHJlY2VudE1ldHJpY3MuZmlsdGVyKG0gPT4gbS5zdGF0dXMgPj0gNDAwKS5sZW5ndGg7XG4gIGNvbnN0IGVycm9yUmF0ZSA9IChlcnJvckNvdW50IC8gdG90YWxSZXF1ZXN0cykgKiAxMDA7XG4gIGNvbnN0IHNsb3dSZXF1ZXN0cyA9IHJlY2VudE1ldHJpY3MuZmlsdGVyKG0gPT4gbS5kdXJhdGlvbiA+IDUwMDApLmxlbmd0aDtcbiAgXG4gIC8vIEdyb3VwIGJ5IGVuZHBvaW50XG4gIGNvbnN0IGVuZHBvaW50U3RhdHM6IFJlY29yZDxzdHJpbmcsIHtcbiAgICByZXF1ZXN0czogbnVtYmVyO1xuICAgIGF2ZXJhZ2VUaW1lOiBudW1iZXI7XG4gICAgZXJyb3JzOiBudW1iZXI7XG4gICAgc2xvd1JlcXVlc3RzOiBudW1iZXI7XG4gIH0+ID0ge307XG4gIFxuICByZWNlbnRNZXRyaWNzLmZvckVhY2gobWV0cmljID0+IHtcbiAgICBpZiAoIWVuZHBvaW50U3RhdHNbbWV0cmljLmVuZHBvaW50XSkge1xuICAgICAgZW5kcG9pbnRTdGF0c1ttZXRyaWMuZW5kcG9pbnRdID0ge1xuICAgICAgICByZXF1ZXN0czogMCxcbiAgICAgICAgYXZlcmFnZVRpbWU6IDAsXG4gICAgICAgIGVycm9yczogMCxcbiAgICAgICAgc2xvd1JlcXVlc3RzOiAwXG4gICAgICB9O1xuICAgIH1cbiAgICBcbiAgICBjb25zdCBzdGF0cyA9IGVuZHBvaW50U3RhdHNbbWV0cmljLmVuZHBvaW50XTtcbiAgICBzdGF0cy5yZXF1ZXN0cysrO1xuICAgIHN0YXRzLmF2ZXJhZ2VUaW1lID0gKHN0YXRzLmF2ZXJhZ2VUaW1lICogKHN0YXRzLnJlcXVlc3RzIC0gMSkgKyBtZXRyaWMuZHVyYXRpb24pIC8gc3RhdHMucmVxdWVzdHM7XG4gICAgXG4gICAgaWYgKG1ldHJpYy5zdGF0dXMgPj0gNDAwKSBzdGF0cy5lcnJvcnMrKztcbiAgICBpZiAobWV0cmljLmR1cmF0aW9uID4gNTAwMCkgc3RhdHMuc2xvd1JlcXVlc3RzKys7XG4gIH0pO1xuICBcbiAgcmV0dXJuIHtcbiAgICB0b3RhbFJlcXVlc3RzLFxuICAgIGF2ZXJhZ2VSZXNwb25zZVRpbWU6IE1hdGgucm91bmQoYXZlcmFnZVJlc3BvbnNlVGltZSksXG4gICAgZXJyb3JSYXRlOiBNYXRoLnJvdW5kKGVycm9yUmF0ZSAqIDEwMCkgLyAxMDAsXG4gICAgc2xvd1JlcXVlc3RzLFxuICAgIGVuZHBvaW50U3RhdHNcbiAgfTtcbn1cblxuLyoqXG4gKiBHZXQgcmVjZW50IGVycm9yc1xuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0UmVjZW50RXJyb3JzKGxpbWl0OiBudW1iZXIgPSA1MCkge1xuICByZXR1cm4gZXJyb3JMb2dzXG4gICAgLnNsaWNlKC1saW1pdClcbiAgICAucmV2ZXJzZSgpIC8vIE1vc3QgcmVjZW50IGZpcnN0XG4gICAgLm1hcChlcnJvciA9PiAoe1xuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZShlcnJvci50aW1lc3RhbXApLnRvSVNPU3RyaW5nKCksXG4gICAgICBlcnJvcjogZXJyb3IuZXJyb3IsXG4gICAgICBlbmRwb2ludDogZXJyb3IuZW5kcG9pbnQsXG4gICAgICBtZXRob2Q6IGVycm9yLm1ldGhvZCxcbiAgICAgIGlwOiBlcnJvci5pcCxcbiAgICAgIGNvbnRleHQ6IGVycm9yLmNvbnRleHRcbiAgICB9KSk7XG59XG5cbi8qKlxuICogSGVhbHRoIGNoZWNrIGZ1bmN0aW9uXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRIZWFsdGhTdGF0dXMoKSB7XG4gIGNvbnN0IHN0YXRzID0gZ2V0UGVyZm9ybWFuY2VTdGF0cyg1ICogNjAgKiAxMDAwKTsgLy8gTGFzdCA1IG1pbnV0ZXNcbiAgY29uc3QgcmVjZW50RXJyb3JzID0gZ2V0UmVjZW50RXJyb3JzKDEwKTtcbiAgXG4gIC8vIERldGVybWluZSBoZWFsdGggc3RhdHVzXG4gIGxldCBzdGF0dXMgPSAnaGVhbHRoeSc7XG4gIGNvbnN0IGlzc3Vlczogc3RyaW5nW10gPSBbXTtcbiAgXG4gIGlmIChzdGF0cy5lcnJvclJhdGUgPiAxMCkge1xuICAgIHN0YXR1cyA9ICd1bmhlYWx0aHknO1xuICAgIGlzc3Vlcy5wdXNoKGBIaWdoIGVycm9yIHJhdGU6ICR7c3RhdHMuZXJyb3JSYXRlfSVgKTtcbiAgfSBlbHNlIGlmIChzdGF0cy5lcnJvclJhdGUgPiA1KSB7XG4gICAgc3RhdHVzID0gJ2RlZ3JhZGVkJztcbiAgICBpc3N1ZXMucHVzaChgRWxldmF0ZWQgZXJyb3IgcmF0ZTogJHtzdGF0cy5lcnJvclJhdGV9JWApO1xuICB9XG4gIFxuICBpZiAoc3RhdHMuYXZlcmFnZVJlc3BvbnNlVGltZSA+IDEwMDAwKSB7XG4gICAgc3RhdHVzID0gJ3VuaGVhbHRoeSc7XG4gICAgaXNzdWVzLnB1c2goYFZlcnkgc2xvdyByZXNwb25zZSB0aW1lOiAke3N0YXRzLmF2ZXJhZ2VSZXNwb25zZVRpbWV9bXNgKTtcbiAgfSBlbHNlIGlmIChzdGF0cy5hdmVyYWdlUmVzcG9uc2VUaW1lID4gNTAwMCkge1xuICAgIGlmIChzdGF0dXMgPT09ICdoZWFsdGh5Jykgc3RhdHVzID0gJ2RlZ3JhZGVkJztcbiAgICBpc3N1ZXMucHVzaChgU2xvdyByZXNwb25zZSB0aW1lOiAke3N0YXRzLmF2ZXJhZ2VSZXNwb25zZVRpbWV9bXNgKTtcbiAgfVxuICBcbiAgaWYgKHN0YXRzLnNsb3dSZXF1ZXN0cyA+IHN0YXRzLnRvdGFsUmVxdWVzdHMgKiAwLjEpIHtcbiAgICBpZiAoc3RhdHVzID09PSAnaGVhbHRoeScpIHN0YXR1cyA9ICdkZWdyYWRlZCc7XG4gICAgaXNzdWVzLnB1c2goYEhpZ2ggbnVtYmVyIG9mIHNsb3cgcmVxdWVzdHM6ICR7c3RhdHMuc2xvd1JlcXVlc3RzfWApO1xuICB9XG4gIFxuICByZXR1cm4ge1xuICAgIHN0YXR1cyxcbiAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICB1cHRpbWU6IHByb2Nlc3MudXB0aW1lKCksXG4gICAgaXNzdWVzLFxuICAgIHN0YXRzOiB7XG4gICAgICByZXF1ZXN0czogc3RhdHMudG90YWxSZXF1ZXN0cyxcbiAgICAgIGF2ZXJhZ2VSZXNwb25zZVRpbWU6IHN0YXRzLmF2ZXJhZ2VSZXNwb25zZVRpbWUsXG4gICAgICBlcnJvclJhdGU6IHN0YXRzLmVycm9yUmF0ZSxcbiAgICAgIHNsb3dSZXF1ZXN0czogc3RhdHMuc2xvd1JlcXVlc3RzXG4gICAgfSxcbiAgICByZWNlbnRFcnJvcnM6IHJlY2VudEVycm9ycy5zbGljZSgwLCAzKSAvLyBPbmx5IHNob3cgMyBtb3N0IHJlY2VudCBlcnJvcnNcbiAgfTtcbn1cblxuLyoqXG4gKiBEYXRhYmFzZSBoZWFsdGggY2hlY2tcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNoZWNrRGF0YWJhc2VIZWFsdGgoKTogUHJvbWlzZTx7IGhlYWx0aHk6IGJvb2xlYW47IGxhdGVuY3k/OiBudW1iZXI7IGVycm9yPzogc3RyaW5nIH0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzdGFydCA9IERhdGUubm93KCk7XG4gICAgXG4gICAgLy8gU2ltcGxlIGRhdGFiYXNlIHF1ZXJ5IHRvIGNoZWNrIGNvbm5lY3Rpdml0eVxuICAgIC8vIFRoaXMgc2hvdWxkIGJlIHJlcGxhY2VkIHdpdGggYWN0dWFsIGRhdGFiYXNlIGhlYWx0aCBjaGVja1xuICAgIGNvbnN0IHsgc3FsIH0gPSBhd2FpdCBpbXBvcnQoJ0B2ZXJjZWwvcG9zdGdyZXMnKTtcbiAgICBhd2FpdCBzcWxgU0VMRUNUIDEgYXMgaGVhbHRoX2NoZWNrYDtcbiAgICBcbiAgICBjb25zdCBsYXRlbmN5ID0gRGF0ZS5ub3coKSAtIHN0YXJ0O1xuICAgIFxuICAgIHJldHVybiB7IGhlYWx0aHk6IHRydWUsIGxhdGVuY3kgfTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICByZXR1cm4geyBcbiAgICAgIGhlYWx0aHk6IGZhbHNlLCBcbiAgICAgIGVycm9yOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdVbmtub3duIGRhdGFiYXNlIGVycm9yJyBcbiAgICB9O1xuICB9XG59XG5cbi8qKlxuICogTWVtb3J5IHVzYWdlIG1vbml0b3JpbmdcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldE1lbW9yeVVzYWdlKCkge1xuICBjb25zdCB1c2FnZSA9IHByb2Nlc3MubWVtb3J5VXNhZ2UoKTtcbiAgXG4gIHJldHVybiB7XG4gICAgcnNzOiBNYXRoLnJvdW5kKHVzYWdlLnJzcyAvIDEwMjQgLyAxMDI0KSwgLy8gTUJcbiAgICBoZWFwVG90YWw6IE1hdGgucm91bmQodXNhZ2UuaGVhcFRvdGFsIC8gMTAyNCAvIDEwMjQpLCAvLyBNQlxuICAgIGhlYXBVc2VkOiBNYXRoLnJvdW5kKHVzYWdlLmhlYXBVc2VkIC8gMTAyNCAvIDEwMjQpLCAvLyBNQlxuICAgIGV4dGVybmFsOiBNYXRoLnJvdW5kKHVzYWdlLmV4dGVybmFsIC8gMTAyNCAvIDEwMjQpLCAvLyBNQlxuICAgIGFycmF5QnVmZmVyczogTWF0aC5yb3VuZCh1c2FnZS5hcnJheUJ1ZmZlcnMgLyAxMDI0IC8gMTAyNCksIC8vIE1CXG4gIH07XG59XG5cbi8qKlxuICogQ2xlYW4gdXAgb2xkIG1ldHJpY3MgYW5kIGVycm9yc1xuICovXG5leHBvcnQgZnVuY3Rpb24gY2xlYW51cE9sZERhdGEoKSB7XG4gIGNvbnN0IG9uZUhvdXJBZ28gPSBEYXRlLm5vdygpIC0gKDYwICogNjAgKiAxMDAwKTtcbiAgXG4gIC8vIFJlbW92ZSBvbGQgcGVyZm9ybWFuY2UgbWV0cmljc1xuICBjb25zdCB2YWxpZE1ldHJpY3MgPSBwZXJmb3JtYW5jZU1ldHJpY3MuZmlsdGVyKG0gPT4gbS50aW1lc3RhbXAgPiBvbmVIb3VyQWdvKTtcbiAgcGVyZm9ybWFuY2VNZXRyaWNzLnNwbGljZSgwLCBwZXJmb3JtYW5jZU1ldHJpY3MubGVuZ3RoLCAuLi52YWxpZE1ldHJpY3MpO1xuICBcbiAgLy8gUmVtb3ZlIG9sZCBlcnJvciBsb2dzXG4gIGNvbnN0IHZhbGlkRXJyb3JzID0gZXJyb3JMb2dzLmZpbHRlcihlID0+IGUudGltZXN0YW1wID4gb25lSG91ckFnbyk7XG4gIGVycm9yTG9ncy5zcGxpY2UoMCwgZXJyb3JMb2dzLmxlbmd0aCwgLi4udmFsaWRFcnJvcnMpO1xufVxuXG4vLyBDbGVhbiB1cCBvbGQgZGF0YSBldmVyeSAxMCBtaW51dGVzXG5zZXRJbnRlcnZhbChjbGVhbnVwT2xkRGF0YSwgMTAgKiA2MCAqIDEwMDApO1xuIl0sIm5hbWVzIjpbInBlcmZvcm1hbmNlTWV0cmljcyIsImVycm9yTG9ncyIsIk1BWF9TVE9SRURfTUVUUklDUyIsIk1BWF9TVE9SRURfRVJST1JTIiwic3RhcnRQZXJmb3JtYW5jZU1vbml0b3JpbmciLCJyZXF1ZXN0Iiwic3RhcnRUaW1lIiwiRGF0ZSIsIm5vdyIsImVuZHBvaW50IiwiVVJMIiwidXJsIiwicGF0aG5hbWUiLCJtZXRob2QiLCJmaW5pc2giLCJzdGF0dXMiLCJlcnJvciIsImR1cmF0aW9uIiwidXNlckFnZW50IiwiaGVhZGVycyIsImdldCIsInVuZGVmaW5lZCIsImlwIiwibWV0cmljIiwidGltZXN0YW1wIiwicHVzaCIsImxlbmd0aCIsInNwbGljZSIsImNvbnNvbGUiLCJ3YXJuIiwibG9nRXJyb3IiLCJjb250ZXh0IiwiZXJyb3JNZXNzYWdlIiwiRXJyb3IiLCJtZXNzYWdlIiwic3RhY2siLCJlcnJvckxvZyIsImFkZGl0aW9uYWxDb250ZXh0IiwiZ2V0UGVyZm9ybWFuY2VTdGF0cyIsInRpbWVSYW5nZSIsImN1dG9mZiIsInJlY2VudE1ldHJpY3MiLCJmaWx0ZXIiLCJtIiwidG90YWxSZXF1ZXN0cyIsImF2ZXJhZ2VSZXNwb25zZVRpbWUiLCJlcnJvclJhdGUiLCJzbG93UmVxdWVzdHMiLCJlbmRwb2ludFN0YXRzIiwicmVkdWNlIiwic3VtIiwiZXJyb3JDb3VudCIsImZvckVhY2giLCJyZXF1ZXN0cyIsImF2ZXJhZ2VUaW1lIiwiZXJyb3JzIiwic3RhdHMiLCJNYXRoIiwicm91bmQiLCJnZXRSZWNlbnRFcnJvcnMiLCJsaW1pdCIsInNsaWNlIiwicmV2ZXJzZSIsIm1hcCIsInRvSVNPU3RyaW5nIiwiZ2V0SGVhbHRoU3RhdHVzIiwicmVjZW50RXJyb3JzIiwiaXNzdWVzIiwidXB0aW1lIiwicHJvY2VzcyIsImNoZWNrRGF0YWJhc2VIZWFsdGgiLCJzdGFydCIsInNxbCIsImxhdGVuY3kiLCJoZWFsdGh5IiwiZ2V0TWVtb3J5VXNhZ2UiLCJ1c2FnZSIsIm1lbW9yeVVzYWdlIiwicnNzIiwiaGVhcFRvdGFsIiwiaGVhcFVzZWQiLCJleHRlcm5hbCIsImFycmF5QnVmZmVycyIsImNsZWFudXBPbGREYXRhIiwib25lSG91ckFnbyIsInZhbGlkTWV0cmljcyIsInZhbGlkRXJyb3JzIiwiZSIsInNldEludGVydmFsIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/monitoring.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fhealth%2Froute&page=%2Fapi%2Fadmin%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fhealth%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();