import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/adminMiddleware';
import { getAllReviews, saveAllReviews, updateReviewStatus } from '@/lib/reviewStorage';

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { action, reviewIds } = await request.json();

    if (!action || !reviewIds || !Array.isArray(reviewIds) || reviewIds.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Invalid request. Action and reviewIds are required.' },
        { status: 400 }
      );
    }

    let result;
    let message = '';

    switch (action) {
      case 'approve':
        result = await bulkUpdateStatus(reviewIds, 'approved');
        message = `${result.count} reviews approved`;
        break;

      case 'reject':
        result = await bulkUpdateStatus(reviewIds, 'rejected');
        message = `${result.count} reviews rejected`;
        break;

      case 'delete':
        result = await bulkDelete(reviewIds);
        message = `${result.count} reviews deleted`;
        break;

      case 'export':
        const exportData = await bulkExport(reviewIds);
        return NextResponse.json({
          success: true,
          message: 'Reviews exported successfully',
          data: exportData
        });

      default:
        return NextResponse.json(
          { success: false, message: 'Invalid action' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message,
      affected_count: result.count
    });

  } catch (error) {
    console.error('Error performing bulk operation:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to perform bulk operation' },
      { status: 500 }
    );
  }
}

async function bulkUpdateStatus(reviewIds: string[], status: 'approved' | 'rejected') {
  try {
    const reviews = await getAllReviews();
    let count = 0;

    const updatedReviews = reviews.map(review => {
      if (reviewIds.includes(review.id)) {
        count++;
        return { ...review, status };
      }
      return review;
    });

    await saveAllReviews(updatedReviews);
    return { count };
  } catch (error) {
    console.error('Error in bulk status update:', error);
    throw error;
  }
}

async function bulkDelete(reviewIds: string[]) {
  try {
    const reviews = await getAllReviews();
    const filteredReviews = reviews.filter(review => !reviewIds.includes(review.id));
    const count = reviews.length - filteredReviews.length;

    await saveAllReviews(filteredReviews);
    return { count };
  } catch (error) {
    console.error('Error in bulk delete:', error);
    throw error;
  }
}

async function bulkExport(reviewIds: string[]) {
  try {
    const reviews = await getAllReviews();
    const selectedReviews = reviews.filter(review => reviewIds.includes(review.id));

    // Format for export
    return selectedReviews.map(review => ({
      id: review.id,
      name: review.name,
      location: review.location,
      email: review.email,
      rating: review.rating,
      title: review.title || '',
      review: review.review,
      vehiclePurchased: review.vehiclePurchased || '',
      purchaseDate: review.purchaseDate || '',
      status: review.status,
      submittedAt: review.submittedAt,
      locale: review.locale
    }));
  } catch (error) {
    console.error('Error in bulk export:', error);
    throw error;
  }
}

// Additional endpoint for getting review analytics
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminAuth = getAdminAuth(request);
    if (!adminAuth.isValid) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const reviews = await getAllReviews();
    
    // Calculate analytics
    const analytics = {
      total: reviews.length,
      byStatus: {
        pending: reviews.filter(r => r.status === 'pending').length,
        approved: reviews.filter(r => r.status === 'approved').length,
        rejected: reviews.filter(r => r.status === 'rejected').length
      },
      byRating: {
        5: reviews.filter(r => r.rating === 5).length,
        4: reviews.filter(r => r.rating === 4).length,
        3: reviews.filter(r => r.rating === 3).length,
        2: reviews.filter(r => r.rating === 2).length,
        1: reviews.filter(r => r.rating === 1).length
      },
      averageRating: reviews.length > 0 
        ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length 
        : 0,
      recentSubmissions: {
        today: reviews.filter(r => isToday(new Date(r.submittedAt))).length,
        thisWeek: reviews.filter(r => isThisWeek(new Date(r.submittedAt))).length,
        thisMonth: reviews.filter(r => isThisMonth(new Date(r.submittedAt))).length
      },
      topLocations: getTopLocations(reviews),
      monthlyTrend: getMonthlyTrend(reviews)
    };

    return NextResponse.json({
      success: true,
      analytics
    });

  } catch (error) {
    console.error('Error fetching review analytics:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch analytics' },
      { status: 500 }
    );
  }
}

function isToday(date: Date): boolean {
  const today = new Date();
  return date.toDateString() === today.toDateString();
}

function isThisWeek(date: Date): boolean {
  const now = new Date();
  const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  return date >= weekAgo;
}

function isThisMonth(date: Date): boolean {
  const now = new Date();
  return date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear();
}

function getTopLocations(reviews: any[]): Array<{ location: string; count: number }> {
  const locationCounts: Record<string, number> = {};
  
  reviews.forEach(review => {
    const location = review.location || 'Unknown';
    locationCounts[location] = (locationCounts[location] || 0) + 1;
  });

  return Object.entries(locationCounts)
    .map(([location, count]) => ({ location, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);
}

function getMonthlyTrend(reviews: any[]): Array<{ month: string; count: number }> {
  const monthCounts: Record<string, number> = {};
  
  reviews.forEach(review => {
    const date = new Date(review.submittedAt);
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    monthCounts[monthKey] = (monthCounts[monthKey] || 0) + 1;
  });

  return Object.entries(monthCounts)
    .map(([month, count]) => ({ month, count }))
    .sort((a, b) => a.month.localeCompare(b.month))
    .slice(-12); // Last 12 months
}
