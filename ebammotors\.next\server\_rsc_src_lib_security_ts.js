"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_security_ts";
exports.ids = ["_rsc_src_lib_security_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/security.ts":
/*!*****************************!*\
  !*** ./src/lib/security.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkRateLimit: () => (/* binding */ checkRateLimit),\n/* harmony export */   cleanupRateLimits: () => (/* binding */ cleanupRateLimits),\n/* harmony export */   containsSuspiciousContent: () => (/* binding */ containsSuspiciousContent),\n/* harmony export */   generateSecureToken: () => (/* binding */ generateSecureToken),\n/* harmony export */   getClientIP: () => (/* binding */ getClientIP),\n/* harmony export */   getSecurityHeaders: () => (/* binding */ getSecurityHeaders),\n/* harmony export */   isSuspiciousRequest: () => (/* binding */ isSuspiciousRequest),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPhone: () => (/* binding */ isValidPhone),\n/* harmony export */   logSecurityEvent: () => (/* binding */ logSecurityEvent),\n/* harmony export */   resetRateLimit: () => (/* binding */ resetRateLimit),\n/* harmony export */   sanitizeInput: () => (/* binding */ sanitizeInput),\n/* harmony export */   validateFileUpload: () => (/* binding */ validateFileUpload)\n/* harmony export */ });\n// Rate limiting configuration\nconst RATE_LIMITS = {\n    auth: {\n        maxAttempts: 5,\n        windowMs: 15 * 60 * 1000\n    },\n    api: {\n        maxRequests: 100,\n        windowMs: 60 * 1000\n    },\n    contact: {\n        maxSubmissions: 3,\n        windowMs: 60 * 60 * 1000\n    },\n    review: {\n        maxSubmissions: 2,\n        windowMs: 60 * 60 * 1000\n    }\n};\n// In-memory rate limit store (use Redis in production)\nconst rateLimitStore = new Map();\n/**\n * Generic rate limiter\n */ function checkRateLimit(identifier, type) {\n    const config = RATE_LIMITS[type];\n    const now = Date.now();\n    const key = `${type}:${identifier}`;\n    const record = rateLimitStore.get(key);\n    // No previous record or window expired\n    if (!record || now > record.resetTime) {\n        const newRecord = {\n            count: 1,\n            resetTime: now + config.windowMs\n        };\n        rateLimitStore.set(key, newRecord);\n        return {\n            allowed: true,\n            remaining: config.maxAttempts - 1,\n            resetTime: newRecord.resetTime\n        };\n    }\n    // Check if limit exceeded\n    if (record.count >= config.maxAttempts) {\n        return {\n            allowed: false,\n            remaining: 0,\n            resetTime: record.resetTime\n        };\n    }\n    // Increment count\n    record.count++;\n    rateLimitStore.set(key, record);\n    return {\n        allowed: true,\n        remaining: config.maxAttempts - record.count,\n        resetTime: record.resetTime\n    };\n}\n/**\n * Reset rate limit for a specific identifier and type\n */ function resetRateLimit(identifier, type) {\n    const key = `${type}:${identifier}`;\n    rateLimitStore.delete(key);\n}\n/**\n * Clean up expired rate limit records\n */ function cleanupRateLimits() {\n    const now = Date.now();\n    for (const [key, record] of rateLimitStore.entries()){\n        if (now > record.resetTime) {\n            rateLimitStore.delete(key);\n        }\n    }\n}\n/**\n * Get client IP address from request\n */ function getClientIP(request) {\n    const forwarded = request.headers.get('x-forwarded-for');\n    const realIP = request.headers.get('x-real-ip');\n    const remoteAddr = request.headers.get('remote-addr');\n    if (forwarded) {\n        return forwarded.split(',')[0].trim();\n    }\n    return realIP || remoteAddr || 'unknown';\n}\n/**\n * Input sanitization\n */ function sanitizeInput(input) {\n    if (typeof input !== 'string') return '';\n    return input.trim().replace(/[<>]/g, '') // Remove potential HTML tags\n    .replace(/javascript:/gi, '') // Remove javascript: protocol\n    .replace(/on\\w+=/gi, '') // Remove event handlers\n    .substring(0, 1000); // Limit length\n}\n/**\n * Validate email format\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email) && email.length <= 254;\n}\n/**\n * Validate phone number format\n */ function isValidPhone(phone) {\n    const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n    return phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g, ''));\n}\n/**\n * Check for suspicious patterns in text\n */ function containsSuspiciousContent(text) {\n    const suspiciousPatterns = [\n        /script/gi,\n        /javascript/gi,\n        /vbscript/gi,\n        /onload/gi,\n        /onerror/gi,\n        /onclick/gi,\n        /<iframe/gi,\n        /<object/gi,\n        /<embed/gi,\n        /eval\\(/gi,\n        /document\\.cookie/gi,\n        /window\\.location/gi\n    ];\n    return suspiciousPatterns.some((pattern)=>pattern.test(text));\n}\n/**\n * Validate file upload\n */ function validateFileUpload(file, allowedTypes, maxSize) {\n    // Check file size\n    if (file.size > maxSize) {\n        return {\n            valid: false,\n            error: `File size exceeds ${maxSize / 1024 / 1024}MB limit`\n        };\n    }\n    // Check file type\n    if (!allowedTypes.includes(file.type)) {\n        return {\n            valid: false,\n            error: `File type ${file.type} not allowed`\n        };\n    }\n    // Check file name for suspicious content\n    if (containsSuspiciousContent(file.name)) {\n        return {\n            valid: false,\n            error: 'File name contains suspicious content'\n        };\n    }\n    return {\n        valid: true\n    };\n}\n/**\n * Generate secure random string\n */ function generateSecureToken(length = 32) {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n    let result = '';\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n/**\n * Security headers for API responses\n */ function getSecurityHeaders() {\n    return {\n        'X-Content-Type-Options': 'nosniff',\n        'X-Frame-Options': 'DENY',\n        'X-XSS-Protection': '1; mode=block',\n        'Referrer-Policy': 'strict-origin-when-cross-origin',\n        'Content-Security-Policy': \"default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';\"\n    };\n}\n/**\n * Log security events\n */ function logSecurityEvent(event, details, request) {\n    const timestamp = new Date().toISOString();\n    const clientIP = getClientIP(request);\n    const userAgent = request.headers.get('user-agent') || 'unknown';\n    console.warn(`[SECURITY] ${timestamp} - ${event}`, {\n        ip: clientIP,\n        userAgent,\n        details,\n        url: request.url\n    });\n}\n/**\n * Check if request is from a suspicious source\n */ function isSuspiciousRequest(request) {\n    const userAgent = request.headers.get('user-agent') || '';\n    const referer = request.headers.get('referer') || '';\n    // Check for common bot patterns\n    const botPatterns = [\n        /bot/i,\n        /crawler/i,\n        /spider/i,\n        /scraper/i,\n        /curl/i,\n        /wget/i\n    ];\n    // Check for suspicious user agents\n    if (botPatterns.some((pattern)=>pattern.test(userAgent))) {\n        return true;\n    }\n    // Check for empty user agent\n    if (!userAgent.trim()) {\n        return true;\n    }\n    // Check for suspicious referers\n    if (referer && !referer.includes(request.headers.get('host') || '')) {\n        // External referer - might be suspicious depending on context\n        return false; // Allow for now, but log\n    }\n    return false;\n}\n// Cleanup expired rate limits every 5 minutes\nsetInterval(cleanupRateLimits, 5 * 60 * 1000);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/security.ts\n");

/***/ })

};
;