/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/security/logs/route";
exports.ids = ["app/api/admin/security/logs/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fsecurity%2Flogs%2Froute&page=%2Fapi%2Fadmin%2Fsecurity%2Flogs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fsecurity%2Flogs%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fsecurity%2Flogs%2Froute&page=%2Fapi%2Fadmin%2Fsecurity%2Flogs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fsecurity%2Flogs%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Desktop_website_ebammotors_src_app_api_admin_security_logs_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/security/logs/route.ts */ \"(rsc)/./src/app/api/admin/security/logs/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/security/logs/route\",\n        pathname: \"/api/admin/security/logs\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/security/logs/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\app\\\\api\\\\admin\\\\security\\\\logs\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Desktop_website_ebammotors_src_app_api_admin_security_logs_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fsecurity%2Flogs%2Froute&page=%2Fapi%2Fadmin%2Fsecurity%2Flogs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fsecurity%2Flogs%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/security/logs/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/admin/security/logs/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_adminMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/adminMiddleware */ \"(rsc)/./src/lib/adminMiddleware.ts\");\n\n\nasync function GET(request) {\n    try {\n        // Verify admin authentication\n        const adminAuth = (0,_lib_adminMiddleware__WEBPACK_IMPORTED_MODULE_1__.getAdminAuth)(request);\n        if (!adminAuth.isValid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Get security logs (mock data for now - in production this would come from a security logging system)\n        const logs = await getSecurityLogs();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            logs\n        });\n    } catch (error) {\n        console.error('Error fetching security logs:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to fetch security logs'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function getSecurityLogs() {\n    // In a real implementation, this would fetch from a security logging system\n    // For now, we'll generate some mock data based on recent activity\n    const mockLogs = [\n        {\n            id: '1',\n            timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(),\n            action: 'login',\n            user: 'admin',\n            ipAddress: '*************',\n            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n            location: 'Tokyo, Japan',\n            status: 'success',\n            details: 'Successful admin login'\n        },\n        {\n            id: '2',\n            timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),\n            action: 'failed_login',\n            user: 'unknown',\n            ipAddress: '************',\n            userAgent: 'curl/7.68.0',\n            location: 'Unknown',\n            status: 'failed',\n            details: 'Invalid credentials provided'\n        },\n        {\n            id: '3',\n            timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),\n            action: 'password_change',\n            user: 'admin',\n            ipAddress: '*************',\n            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n            location: 'Tokyo, Japan',\n            status: 'success',\n            details: 'Password successfully changed'\n        },\n        {\n            id: '4',\n            timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(),\n            action: 'settings_changed',\n            user: 'admin',\n            ipAddress: '*************',\n            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n            location: 'Tokyo, Japan',\n            status: 'success',\n            details: 'Security settings updated'\n        },\n        {\n            id: '5',\n            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),\n            action: 'failed_login',\n            user: 'admin',\n            ipAddress: '*************',\n            userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',\n            location: 'Unknown',\n            status: 'failed',\n            details: 'Multiple failed login attempts detected'\n        },\n        {\n            id: '6',\n            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(),\n            action: 'logout',\n            user: 'admin',\n            ipAddress: '*************',\n            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n            location: 'Tokyo, Japan',\n            status: 'success',\n            details: 'User logged out successfully'\n        },\n        {\n            id: '7',\n            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(),\n            action: 'login',\n            user: 'admin',\n            ipAddress: '*************',\n            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n            location: 'Tokyo, Japan',\n            status: 'success',\n            details: 'Successful admin login'\n        },\n        {\n            id: '8',\n            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 12).toISOString(),\n            action: 'user_created',\n            user: 'admin',\n            ipAddress: '*************',\n            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n            location: 'Tokyo, Japan',\n            status: 'success',\n            details: 'New admin user created: moderator1'\n        },\n        {\n            id: '9',\n            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),\n            action: 'failed_login',\n            user: 'unknown',\n            ipAddress: '************',\n            userAgent: 'python-requests/2.25.1',\n            location: 'Unknown',\n            status: 'failed',\n            details: 'Automated attack attempt detected'\n        },\n        {\n            id: '10',\n            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(),\n            action: 'settings_changed',\n            user: 'admin',\n            ipAddress: '*************',\n            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n            location: 'Tokyo, Japan',\n            status: 'success',\n            details: 'Password policy updated'\n        }\n    ];\n    return mockLogs.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n}\nasync function POST(request) {\n    try {\n        // Verify admin authentication\n        const adminAuth = (0,_lib_adminMiddleware__WEBPACK_IMPORTED_MODULE_1__.getAdminAuth)(request);\n        if (!adminAuth.isValid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { action, user, ipAddress, userAgent, details, status = 'success' } = await request.json();\n        if (!action || !user || !ipAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        // In a real implementation, this would save to a security logging system\n        const logEntry = {\n            id: Date.now().toString(),\n            timestamp: new Date().toISOString(),\n            action,\n            user,\n            ipAddress,\n            userAgent: userAgent || 'Unknown',\n            location: 'Unknown',\n            status,\n            details: details || `${action} performed by ${user}`\n        };\n        console.log('Security log entry:', logEntry);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Security log entry created',\n            logEntry\n        });\n    } catch (error) {\n        console.error('Error creating security log:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to create security log'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/security/logs/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/adminMiddleware.ts":
/*!************************************!*\
  !*** ./src/lib/adminMiddleware.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAdminAuth: () => (/* binding */ getAdminAuth),\n/* harmony export */   getAdminFromRequest: () => (/* binding */ getAdminFromRequest),\n/* harmony export */   verifyLegacyAdminKey: () => (/* binding */ verifyLegacyAdminKey),\n/* harmony export */   withAdminAuth: () => (/* binding */ withAdminAuth)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\n/**\n * Middleware to verify admin authentication for API routes\n */ function withAdminAuth(handler) {\n    return async (request, context)=>{\n        try {\n            // Get authentication from headers or cookies\n            const authHeader = request.headers.get('authorization');\n            const sessionId = request.cookies.get('admin_session')?.value;\n            // Verify authentication\n            const authResult = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyAdminAuth)(authHeader, sessionId);\n            if (!authResult.isValid) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: authResult.message\n                }, {\n                    status: 401\n                });\n            }\n            // Add admin info to request headers for the handler\n            const requestWithAuth = new next_server__WEBPACK_IMPORTED_MODULE_0__.NextRequest(request.url, {\n                method: request.method,\n                headers: {\n                    ...Object.fromEntries(request.headers.entries()),\n                    'x-admin-id': authResult.adminId || 'admin',\n                    'x-admin-authenticated': 'true'\n                },\n                body: request.body\n            });\n            return handler(requestWithAuth, context);\n        } catch (error) {\n            console.error('Admin middleware error:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Authentication error'\n            }, {\n                status: 500\n            });\n        }\n    };\n}\n/**\n * Extract admin authentication from request\n */ function getAdminFromRequest(request) {\n    const adminId = request.headers.get('x-admin-id') || 'admin';\n    const isAuthenticated = request.headers.get('x-admin-authenticated') === 'true';\n    return {\n        adminId,\n        isAuthenticated\n    };\n}\n/**\n * Verify admin authentication for legacy API routes that use adminKey\n */ function verifyLegacyAdminKey(adminKey) {\n    const validAdminKey = process.env.ADMIN_PASSWORD || 'admin123';\n    return adminKey === validAdminKey;\n}\n/**\n * Get admin authentication from request (supports both new and legacy methods)\n */ function getAdminAuth(request, body) {\n    // Try new authentication method first\n    const authHeader = request.headers.get('authorization');\n    const sessionId = request.cookies.get('admin_session')?.value;\n    const authResult = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.verifyAdminAuth)(authHeader, sessionId);\n    if (authResult.isValid) {\n        return {\n            isValid: true,\n            adminId: authResult.adminId,\n            method: 'token/session'\n        };\n    }\n    // Fall back to legacy adminKey method\n    const adminKey = body?.adminKey || request.nextUrl.searchParams.get('adminKey');\n    if (adminKey && verifyLegacyAdminKey(adminKey)) {\n        return {\n            isValid: true,\n            adminId: 'admin',\n            method: 'legacy'\n        };\n    }\n    return {\n        isValid: false,\n        method: 'none'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/adminMiddleware.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateAdmin: () => (/* binding */ authenticateAdmin),\n/* harmony export */   checkAuthRateLimit: () => (/* binding */ checkAuthRateLimit),\n/* harmony export */   createAdminSession: () => (/* binding */ createAdminSession),\n/* harmony export */   destroyAdminSession: () => (/* binding */ destroyAdminSession),\n/* harmony export */   generateAdminToken: () => (/* binding */ generateAdminToken),\n/* harmony export */   getAdminPasswordHash: () => (/* binding */ getAdminPasswordHash),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   resetAuthRateLimit: () => (/* binding */ resetAuthRateLimit),\n/* harmony export */   validateAdminSession: () => (/* binding */ validateAdminSession),\n/* harmony export */   verifyAdminAuth: () => (/* binding */ verifyAdminAuth),\n/* harmony export */   verifyAdminToken: () => (/* binding */ verifyAdminToken),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Security configuration\nconst SALT_ROUNDS = 12;\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';\nconst JWT_EXPIRES_IN = '24h';\nconst SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n// In-memory session store (replace with Redis in production)\nconst activeSessions = new Map();\n/**\n * Hash a password using bcrypt\n */ async function hashPassword(password) {\n    try {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, SALT_ROUNDS);\n    } catch (error) {\n        console.error('Error hashing password:', error);\n        throw new Error('Failed to hash password');\n    }\n}\n/**\n * Verify a password against its hash\n */ async function verifyPassword(password, hash) {\n    try {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hash);\n    } catch (error) {\n        console.error('Error verifying password:', error);\n        return false;\n    }\n}\n/**\n * Generate a JWT token for admin authentication\n */ function generateAdminToken(adminId = 'admin') {\n    try {\n        const payload = {\n            id: adminId,\n            isAdmin: true,\n            iat: Math.floor(Date.now() / 1000)\n        };\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN\n        });\n    } catch (error) {\n        console.error('Error generating token:', error);\n        throw new Error('Failed to generate authentication token');\n    }\n}\n/**\n * Verify and decode a JWT token\n */ function verifyAdminToken(token) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        if (decoded.isAdmin) {\n            return {\n                id: decoded.id,\n                isAdmin: decoded.isAdmin\n            };\n        }\n        return null;\n    } catch (error) {\n        // Token is invalid or expired\n        return null;\n    }\n}\n/**\n * Create a new admin session\n */ function createAdminSession(adminId = 'admin') {\n    const sessionId = generateSessionId();\n    const now = Date.now();\n    const session = {\n        id: adminId,\n        isAdmin: true,\n        createdAt: now,\n        expiresAt: now + SESSION_TIMEOUT,\n        lastActivity: now\n    };\n    activeSessions.set(sessionId, session);\n    // Clean up expired sessions\n    cleanupExpiredSessions();\n    return sessionId;\n}\n/**\n * Validate an admin session\n */ function validateAdminSession(sessionId) {\n    const session = activeSessions.get(sessionId);\n    if (!session) {\n        return null;\n    }\n    const now = Date.now();\n    // Check if session has expired\n    if (now > session.expiresAt) {\n        activeSessions.delete(sessionId);\n        return null;\n    }\n    // Update last activity\n    session.lastActivity = now;\n    activeSessions.set(sessionId, session);\n    return session;\n}\n/**\n * Destroy an admin session\n */ function destroyAdminSession(sessionId) {\n    return activeSessions.delete(sessionId);\n}\n/**\n * Generate a secure session ID\n */ function generateSessionId() {\n    return `admin_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;\n}\n/**\n * Clean up expired sessions\n */ function cleanupExpiredSessions() {\n    const now = Date.now();\n    for (const [sessionId, session] of activeSessions.entries()){\n        if (now > session.expiresAt) {\n            activeSessions.delete(sessionId);\n        }\n    }\n}\n/**\n * Get admin password hash from environment\n * In production, this should be stored in a secure database\n */ function getAdminPasswordHash() {\n    // For backward compatibility, check if password is already hashed\n    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';\n    // If it starts with $2a$, $2b$, or $2y$, it's already a bcrypt hash\n    if (adminPassword.startsWith('$2a$') || adminPassword.startsWith('$2b$') || adminPassword.startsWith('$2y$')) {\n        return adminPassword;\n    }\n    // For development/migration: return the plain password (will be handled in auth route)\n    return adminPassword;\n}\n/**\n * Secure admin authentication\n */ async function authenticateAdmin(password) {\n    try {\n        const adminPasswordHash = getAdminPasswordHash();\n        let isValid = false;\n        // Check if stored password is hashed or plain text\n        if (adminPasswordHash.startsWith('$2a$') || adminPasswordHash.startsWith('$2b$') || adminPasswordHash.startsWith('$2y$')) {\n            // Password is hashed, use bcrypt comparison\n            isValid = await verifyPassword(password, adminPasswordHash);\n        } else {\n            // Password is plain text (development/migration), use direct comparison\n            isValid = password === adminPasswordHash;\n        }\n        if (isValid) {\n            const token = generateAdminToken();\n            const sessionId = createAdminSession();\n            return {\n                success: true,\n                token,\n                sessionId,\n                message: 'Authentication successful'\n            };\n        } else {\n            return {\n                success: false,\n                message: 'Invalid credentials'\n            };\n        }\n    } catch (error) {\n        console.error('Authentication error:', error);\n        return {\n            success: false,\n            message: 'Authentication failed'\n        };\n    }\n}\n/**\n * Middleware to verify admin authentication\n */ function verifyAdminAuth(authHeader, sessionId) {\n    // Check JWT token\n    if (authHeader && authHeader.startsWith('Bearer ')) {\n        const token = authHeader.substring(7);\n        const decoded = verifyAdminToken(token);\n        if (decoded) {\n            return {\n                isValid: true,\n                adminId: decoded.id,\n                message: 'Token authentication successful'\n            };\n        }\n    }\n    // Check session ID\n    if (sessionId) {\n        const session = validateAdminSession(sessionId);\n        if (session) {\n            return {\n                isValid: true,\n                adminId: session.id,\n                message: 'Session authentication successful'\n            };\n        }\n    }\n    return {\n        isValid: false,\n        message: 'Authentication required'\n    };\n}\n/**\n * Rate limiting for authentication attempts\n */ const authAttempts = new Map();\nconst MAX_AUTH_ATTEMPTS = 5;\nconst AUTH_LOCKOUT_TIME = 15 * 60 * 1000; // 15 minutes\nfunction checkAuthRateLimit(ip) {\n    const now = Date.now();\n    const attempts = authAttempts.get(ip);\n    if (!attempts) {\n        authAttempts.set(ip, {\n            count: 1,\n            lastAttempt: now\n        });\n        return {\n            allowed: true,\n            remainingAttempts: MAX_AUTH_ATTEMPTS - 1\n        };\n    }\n    // Reset if lockout time has passed\n    if (now - attempts.lastAttempt > AUTH_LOCKOUT_TIME) {\n        authAttempts.set(ip, {\n            count: 1,\n            lastAttempt: now\n        });\n        return {\n            allowed: true,\n            remainingAttempts: MAX_AUTH_ATTEMPTS - 1\n        };\n    }\n    // Check if max attempts exceeded\n    if (attempts.count >= MAX_AUTH_ATTEMPTS) {\n        const lockoutTime = AUTH_LOCKOUT_TIME - (now - attempts.lastAttempt);\n        return {\n            allowed: false,\n            remainingAttempts: 0,\n            lockoutTime\n        };\n    }\n    // Increment attempt count\n    attempts.count++;\n    attempts.lastAttempt = now;\n    authAttempts.set(ip, attempts);\n    return {\n        allowed: true,\n        remainingAttempts: MAX_AUTH_ATTEMPTS - attempts.count\n    };\n}\nfunction resetAuthRateLimit(ip) {\n    authAttempts.delete(ip);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fsecurity%2Flogs%2Froute&page=%2Fapi%2Fadmin%2Fsecurity%2Flogs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fsecurity%2Flogs%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDesktop%5Cwebsite%5Cebammotors&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();